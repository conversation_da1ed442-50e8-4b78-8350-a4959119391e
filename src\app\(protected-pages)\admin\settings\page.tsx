'use client'

import { useState } from 'react'
import Tabs from '@/components/ui/Tabs'
import {
    PiUserCircleDuotone,
    PiLighthouse,
    PiGearDuotone,
    PiShieldCheckDuotone,
    PiBellDuotone,
} from 'react-icons/pi'

// Tab components
import UserManagementTab from './tabs/UserManagementTab'
import HubTab from './tabs/HubTab'
import GeneralSettingsTab from './tabs/GeneralSettingsTab'
import SecuritySettingsTab from './tabs/SecuritySettingsTab'
import NotificationSettingsTab from './tabs/NotificationSettingsTab'

const AdminSettingsPage = () => {
    const [activeTab, setActiveTab] = useState('users')

    return (
        <div className="p-6">
            <div className="mb-6">
                <h4 className="text-2xl font-bold mb-2">Admin Settings</h4>
                <p className="text-gray-500 dark:text-gray-400">
                    Manage users, system settings, and organization
                    configuration
                </p>
            </div>

            <Tabs
                value={activeTab}
                onChange={(val) => setActiveTab(val as string)}
            >
                <Tabs.TabList>
                    <Tabs.TabNav value="users" icon={<PiUserCircleDuotone />}>
                        User Management
                    </Tabs.TabNav>
                    <Tabs.TabNav value="hub" icon={<PiLighthouse />}>
                        Hub & Billing
                    </Tabs.TabNav>
                    <Tabs.TabNav value="general" icon={<PiGearDuotone />}>
                        General
                    </Tabs.TabNav>
                    <Tabs.TabNav
                        value="security"
                        icon={<PiShieldCheckDuotone />}
                    >
                        Security
                    </Tabs.TabNav>
                    <Tabs.TabNav value="notifications" icon={<PiBellDuotone />}>
                        Notifications
                    </Tabs.TabNav>
                </Tabs.TabList>
                <div className="mt-4">
                    <Tabs.TabContent value="users">
                        <UserManagementTab />
                    </Tabs.TabContent>
                    <Tabs.TabContent value="hub">
                        <HubTab />
                    </Tabs.TabContent>
                    <Tabs.TabContent value="general">
                        <GeneralSettingsTab />
                    </Tabs.TabContent>
                    <Tabs.TabContent value="security">
                        <SecuritySettingsTab />
                    </Tabs.TabContent>
                    <Tabs.TabContent value="notifications">
                        <NotificationSettingsTab />
                    </Tabs.TabContent>
                </div>
            </Tabs>
        </div>
    )
}

export default AdminSettingsPage
