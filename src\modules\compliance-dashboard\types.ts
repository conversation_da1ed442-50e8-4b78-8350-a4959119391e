// Compliance Dashboard module types
export interface DashboardMetrics {
    frameworks: {
        total: number
        active: number
        inProgress: number
        notStarted: number
        averageProgress: number
    }
    controls: {
        total: number
        compliant: number
        nonCompliant: number
        inProgress: number
        notStarted: number
        needsReview: number
        completionRate: number
    }
    tasks: {
        total: number
        open: number
        inProgress: number
        completed: number
        overdue: number
        dueThisWeek: number
    }
    risks: {
        total: number
        critical: number
        high: number
        medium: number
        low: number
        unmitigated: number
    }
    evidence: {
        total: number
        approved: number
        pending: number
        expired: number
        readinessRate: number
    }
}

export interface TrendData {
    date: string
    completionRate: number
    newTasks: number
    completedTasks: number
    newRisks: number
    mitigatedRisks: number
}

export interface FrameworkProgress {
    id: string
    name: string
    category: string
    progress: number
    controlsTotal: number
    controlsCompliant: number
    lastUpdated: string
    status: string
    dueDate?: string
}

export interface RecentActivity {
    id: string
    type:
        | 'task_completed'
        | 'control_updated'
        | 'evidence_uploaded'
        | 'risk_identified'
        | 'framework_added'
    title: string
    description: string
    user: string
    timestamp: string
    entityId: string
    entityType: 'task' | 'control' | 'evidence' | 'risk' | 'framework'
}

export interface ComplianceAlert {
    id: string
    type:
        | 'overdue_task'
        | 'expired_evidence'
        | 'high_risk'
        | 'missing_evidence'
        | 'review_required'
    severity: 'low' | 'medium' | 'high' | 'critical'
    title: string
    description: string
    entityId: string
    entityType: 'task' | 'control' | 'evidence' | 'risk' | 'framework'
    createdAt: string
    acknowledged: boolean
}

export interface TeamWorkload {
    userId: string
    name: string
    email: string
    avatar?: string
    openTasks: number
    overdueTasks: number
    completedThisMonth: number
    assignedControls: number
    workloadScore: number
}

// Re-export shared types for convenience
export type {
    Framework,
    Control,
    Task,
    Risk,
    Evidence,
    ComplianceMetrics,
} from '@/shared/types/compliance'
