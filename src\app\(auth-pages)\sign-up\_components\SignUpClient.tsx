'use client'

import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'
import SignUp from '@/components/auth/SignUp'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import type { OnSignUpPayload } from '@/components/auth/SignUp'

const SignUpClient = () => {
    const { signUp } = useAuth()
    const router = useRouter()

    const handlSignUp = async ({
        values,
        setSubmitting,
        setMessage,
    }: OnSignUpPayload) => {
        try {
            setSubmitting(true)
            const { error } = await signUp(
                values.email,
                values.password,
                values.userName,
            )

            if (error) {
                setMessage(error.message || 'Sign up failed')
            } else {
                toast.push(
                    <Notification title="Account created!" type="success">
                        Please check your email to verify your account
                    </Notification>,
                )
                router.push('/sign-in')
            }
        } catch {
            setMessage('An unexpected error occurred')
        } finally {
            setSubmitting(false)
        }
    }

    return <SignUp onSignUp={handlSignUp} />
}

export default SignUpClient
