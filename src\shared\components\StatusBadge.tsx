import { ReactNode } from 'react'
import classNames from '@/utils/classNames'

interface StatusBadgeProps {
    status: string
    variant?: 'default' | 'outline' | 'solid'
    size?: 'sm' | 'md' | 'lg'
    children?: ReactNode
    className?: string
}

const getStatusStyles = (status: string, variant: string = 'default') => {
    const baseStyles = {
        Active: {
            default:
                'bg-emerald-100 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400 dark:border-emerald-500',
            outline:
                'border-emerald-500 text-emerald-700 dark:text-emerald-400',
            solid: 'bg-emerald-500 text-white',
        },
        Compliant: {
            default:
                'bg-emerald-100 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400 dark:border-emerald-500',
            outline:
                'border-emerald-500 text-emerald-700 dark:text-emerald-400',
            solid: 'bg-emerald-500 text-white',
        },
        'In Progress': {
            default:
                'bg-amber-100 text-amber-700 border-amber-200 dark:bg-amber-900/30 dark:text-amber-400 dark:border-amber-500',
            outline: 'border-amber-500 text-amber-700 dark:text-amber-400',
            solid: 'bg-amber-500 text-white',
        },
        Implemented: {
            default:
                'bg-blue-100 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-500',
            outline: 'border-blue-500 text-blue-700 dark:text-blue-400',
            solid: 'bg-blue-500 text-white',
        },
        'Not Started': {
            default:
                'bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-700/50 dark:text-gray-400 dark:border-gray-500',
            outline: 'border-gray-400 text-gray-700 dark:text-gray-400',
            solid: 'bg-gray-500 text-white',
        },
        'Needs Review': {
            default:
                'bg-orange-100 text-orange-700 border-orange-200 dark:bg-orange-900/30 dark:text-orange-400 dark:border-orange-500',
            outline: 'border-orange-500 text-orange-700 dark:text-orange-400',
            solid: 'bg-orange-500 text-white',
        },
        'Non-Compliant': {
            default:
                'bg-red-100 text-red-700 border-red-200 dark:bg-red-900/30 dark:text-red-400 dark:border-red-500',
            outline: 'border-red-500 text-red-700 dark:text-red-400',
            solid: 'bg-red-500 text-white',
        },
        Overdue: {
            default:
                'bg-red-100 text-red-700 border-red-200 dark:bg-red-900/30 dark:text-red-400 dark:border-red-500',
            outline: 'border-red-500 text-red-700 dark:text-red-400',
            solid: 'bg-red-500 text-white',
        },
        Completed: {
            default:
                'bg-emerald-100 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400 dark:border-emerald-500',
            outline:
                'border-emerald-500 text-emerald-700 dark:text-emerald-400',
            solid: 'bg-emerald-500 text-white',
        },
        Open: {
            default:
                'bg-blue-100 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-500',
            outline: 'border-blue-500 text-blue-700 dark:text-blue-400',
            solid: 'bg-blue-500 text-white',
        },
        High: {
            default:
                'bg-red-100 text-red-700 border-red-200 dark:bg-red-900/30 dark:text-red-400 dark:border-red-500',
            outline: 'border-red-500 text-red-700 dark:text-red-400',
            solid: 'bg-red-500 text-white',
        },
        Medium: {
            default:
                'bg-amber-100 text-amber-700 border-amber-200 dark:bg-amber-900/30 dark:text-amber-400 dark:border-amber-500',
            outline: 'border-amber-500 text-amber-700 dark:text-amber-400',
            solid: 'bg-amber-500 text-white',
        },
        Low: {
            default:
                'bg-green-100 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-500',
            outline: 'border-green-500 text-green-700 dark:text-green-400',
            solid: 'bg-green-500 text-white',
        },
        Critical: {
            default:
                'bg-purple-100 text-purple-700 border-purple-200 dark:bg-purple-900/30 dark:text-purple-400 dark:border-purple-500',
            outline: 'border-purple-500 text-purple-700 dark:text-purple-400',
            solid: 'bg-purple-500 text-white',
        },
        Approved: {
            default:
                'bg-emerald-100 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400 dark:border-emerald-500',
            outline:
                'border-emerald-500 text-emerald-700 dark:text-emerald-400',
            solid: 'bg-emerald-500 text-white',
        },
        Pending: {
            default:
                'bg-amber-100 text-amber-700 border-amber-200 dark:bg-amber-900/30 dark:text-amber-400 dark:border-amber-500',
            outline: 'border-amber-500 text-amber-700 dark:text-amber-400',
            solid: 'bg-amber-500 text-white',
        },
        Rejected: {
            default:
                'bg-red-100 text-red-700 border-red-200 dark:bg-red-900/30 dark:text-red-400 dark:border-red-500',
            outline: 'border-red-500 text-red-700 dark:text-red-400',
            solid: 'bg-red-500 text-white',
        },
        Expired: {
            default:
                'bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-700/50 dark:text-gray-400 dark:border-gray-500',
            outline: 'border-gray-400 text-gray-700 dark:text-gray-400',
            solid: 'bg-gray-500 text-white',
        },
        Archived: {
            default:
                'bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-700/50 dark:text-gray-400 dark:border-gray-500',
            outline: 'border-gray-400 text-gray-700 dark:text-gray-400',
            solid: 'bg-gray-500 text-white',
        },
    }

    return (
        baseStyles[status as keyof typeof baseStyles]?.[
            variant as keyof (typeof baseStyles)[keyof typeof baseStyles]
        ] ||
        baseStyles['Not Started'][
            variant as keyof (typeof baseStyles)['Not Started']
        ]
    )
}

const getSizeStyles = (size: string) => {
    switch (size) {
        case 'sm':
            return 'px-2 py-0.5 text-xs'
        case 'lg':
            return 'px-4 py-2 text-sm'
        default:
            return 'px-3 py-1 text-xs'
    }
}

const StatusBadge = ({
    status,
    variant = 'default',
    size = 'md',
    children,
    className,
}: StatusBadgeProps) => {
    const statusStyles = getStatusStyles(status, variant)
    const sizeStyles = getSizeStyles(size)

    const baseClasses = 'inline-flex items-center font-medium rounded-md border'

    return (
        <span
            className={classNames(
                baseClasses,
                statusStyles,
                sizeStyles,
                className,
            )}
        >
            {children || status}
        </span>
    )
}

export default StatusBadge
