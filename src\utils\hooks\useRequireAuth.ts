'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import appConfig from '@/configs/app.config'

export function useRequireAuth() {
    const { user, loading } = useAuth()
    const router = useRouter()

    useEffect(() => {
        if (!loading && !user) {
            router.push(appConfig.unAuthenticatedEntryPath)
        }
    }, [user, loading, router])

    return { user, loading }
}
