'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { StatusBadge } from '@/shared/components'
import { Policy } from '@/shared/types/compliance'
import {
    HiOutlineDocumentText,
    HiOutlineCalendar,
    HiOutlineEye,
    HiOutlineCog,
    HiOutlineClipboardCheck,
    HiOutlineExclamationCircle,
    HiOutlineCheckCircle,
} from 'react-icons/hi'

interface PolicyCardProps {
    policy: Policy
    onView?: (policy: Policy) => void
    onEdit?: (policy: Policy) => void
    onViewControls?: (policy: Policy) => void
    onViewAcknowledgments?: (policy: Policy) => void
    compact?: boolean
}

const PolicyCard = ({
    policy,
    onView,
    onEdit,
    onViewControls,
    onViewAcknowledgments,
    compact = false,
}: PolicyCardProps) => {
    const [isHovered, setIsHovered] = useState(false)

    const getCategoryColor = (category: string) => {
        const colors = {
            Security: 'bg-red-100 text-red-700 border-red-200',
            Privacy: 'bg-purple-100 text-purple-700 border-purple-200',
            HR: 'bg-blue-100 text-blue-700 border-blue-200',
            IT: 'bg-green-100 text-green-700 border-green-200',
            Finance: 'bg-amber-100 text-amber-700 border-amber-200',
            Operations: 'bg-indigo-100 text-indigo-700 border-indigo-200',
            Compliance: 'bg-pink-100 text-pink-700 border-pink-200',
        }
        return colors[category as keyof typeof colors] || colors['Security']
    }

    const isReviewOverdue = () => {
        if (!policy.nextReviewDate) return false
        return new Date(policy.nextReviewDate) < new Date()
    }

    const isReviewDueSoon = () => {
        if (!policy.nextReviewDate) return false
        const reviewDate = new Date(policy.nextReviewDate)
        const today = new Date()
        const daysUntilReview = Math.ceil(
            (reviewDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24),
        )
        return daysUntilReview <= 30 && daysUntilReview > 0
    }

    if (compact) {
        return (
            <div
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-colors border-gray-200 dark:border-gray-700"
                onClick={() => onView?.(policy)}
            >
                <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                        <HiOutlineDocumentText className="w-4 h-4 text-gray-500" />
                        {isReviewOverdue() && (
                            <HiOutlineExclamationCircle className="w-4 h-4 text-red-500" />
                        )}
                        {isReviewDueSoon() && (
                            <HiOutlineCalendar className="w-4 h-4 text-amber-500" />
                        )}
                    </div>

                    <div className="flex-1 min-w-0">
                        <h4 className="font-medium truncate">{policy.title}</h4>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                            <span>v{policy.version}</span>
                            <span>•</span>
                            <span>{policy.category}</span>
                            {policy.nextReviewDate && (
                                <>
                                    <span>•</span>
                                    <span>
                                        Review:{' '}
                                        {new Date(
                                            policy.nextReviewDate,
                                        ).toLocaleDateString()}
                                    </span>
                                </>
                            )}
                        </div>
                    </div>
                </div>

                <div className="flex items-center gap-2">
                    <StatusBadge status={policy.status} size="sm" />
                </div>
            </div>
        )
    }

    return (
        <Card
            clickable
            className="h-full transition-all duration-200 hover:shadow-lg"
            bodyClass="h-full flex flex-col p-4"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={() => onView?.(policy)}
        >
            {/* Header */}
            <div className="flex justify-between items-start mb-3">
                <div className="flex items-center gap-2">
                    <HiOutlineDocumentText className="w-5 h-5 text-gray-500" />
                    <StatusBadge status={policy.status} size="sm" />
                    {isReviewOverdue() && (
                        <HiOutlineExclamationCircle
                            className="w-4 h-4 text-red-500"
                            title="Review Overdue"
                        />
                    )}
                    {isReviewDueSoon() && (
                        <HiOutlineCalendar
                            className="w-4 h-4 text-amber-500"
                            title="Review Due Soon"
                        />
                    )}
                </div>
                <span className="text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded font-medium">
                    v{policy.version}
                </span>
            </div>

            {/* Policy Info */}
            <div className="flex-1">
                <h4 className="font-semibold mb-2 line-clamp-2">
                    {policy.title}
                </h4>

                {policy.category && (
                    <div
                        className={`inline-block px-2 py-1 rounded-md text-xs font-medium border mb-3 ${getCategoryColor(policy.category)}`}
                    >
                        {policy.category}
                    </div>
                )}

                {policy.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-3">
                        {policy.description}
                    </p>
                )}

                {/* Dates */}
                <div className="space-y-2 text-xs mb-3">
                    {policy.effectiveDate && (
                        <div className="flex justify-between text-gray-500">
                            <span>Effective:</span>
                            <span>
                                {new Date(
                                    policy.effectiveDate,
                                ).toLocaleDateString()}
                            </span>
                        </div>
                    )}
                    {policy.nextReviewDate && (
                        <div
                            className={`flex justify-between ${
                                isReviewOverdue()
                                    ? 'text-red-600'
                                    : isReviewDueSoon()
                                      ? 'text-amber-600'
                                      : 'text-gray-500'
                            }`}
                        >
                            <span>Next Review:</span>
                            <span>
                                {new Date(
                                    policy.nextReviewDate,
                                ).toLocaleDateString()}
                            </span>
                        </div>
                    )}
                </div>

                {/* Acknowledgments */}
                {policy.acknowledgments.length > 0 && (
                    <div className="flex items-center gap-2 text-xs text-gray-500 mb-3">
                        <HiOutlineCheckCircle className="w-3 h-3" />
                        <span>
                            {policy.acknowledgments.length} acknowledgments
                        </span>
                    </div>
                )}
            </div>

            {/* Meta Info */}
            <div className="flex justify-between items-center mb-3 text-xs text-gray-500">
                <div className="flex items-center gap-3">
                    <div className="flex items-center gap-1">
                        <HiOutlineClipboardCheck className="w-3 h-3" />
                        <span>{policy.controlIds.length} controls</span>
                    </div>
                    {policy.owner && <div>Owner: {policy.owner}</div>}
                </div>
            </div>

            {/* Footer */}
            <div className="flex justify-between items-center pt-3 border-t border-gray-200 dark:border-gray-700">
                <div className="text-xs text-gray-500">
                    Updated: {new Date(policy.updatedAt).toLocaleDateString()}
                </div>

                {/* Action Buttons */}
                <div
                    className={`flex gap-1 transition-opacity duration-200 ${isHovered ? 'opacity-100' : 'opacity-0'}`}
                >
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineEye />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onView?.(policy)
                        }}
                        title="View Policy"
                    />
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineCheckCircle />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onViewAcknowledgments?.(policy)
                        }}
                        title="View Acknowledgments"
                    />
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineClipboardCheck />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onViewControls?.(policy)
                        }}
                        title="View Controls"
                    />
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineCog />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onEdit?.(policy)
                        }}
                        title="Settings"
                    />
                </div>
            </div>
        </Card>
    )
}

export default PolicyCard
