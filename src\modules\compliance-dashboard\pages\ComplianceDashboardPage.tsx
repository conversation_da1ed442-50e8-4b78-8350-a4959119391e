'use client'

import { useAuth } from '@/contexts/AuthContext'
import Card from '@/components/ui/Card'
import Link from 'next/link'
import {
    PiChartBarDuotone,
    PiClipboardTextDuotone,
    PiGearSixDuotone,
    PiRocketLaunchDuotone,
    PiShieldCheckDuotone,
    PiUserCircleDuotone
} from 'react-icons/pi'

const ComplianceDashboardPage = () => {
    const { user } = useAuth()

    const quickLinks = [
        {
            title: 'Framework Management',
            description: 'Manage compliance frameworks like PCI DSS',
            icon: <PiClipboardTextDuotone className="text-3xl" />,
            href: '/dashboard/framework-management',
            color: 'text-blue-600 dark:text-blue-400',
            bgColor: 'bg-blue-50 dark:bg-blue-900/20',
        },
        {
            title: 'Admin Settings',
            description: 'Configure system settings and preferences',
            icon: <PiGearSixDuotone className="text-3xl" />,
            href: '/admin/settings',
            color: 'text-gray-600 dark:text-gray-400',
            bgColor: 'bg-gray-50 dark:bg-gray-900/20',
            adminOnly: true,
        },
    ]

    // Filter links based on user permissions
    const availableLinks = quickLinks.filter(link => {
        if (link.adminOnly) {
            // For now, we'll assume all authenticated users can access admin
            // TODO: Implement proper role checking
            return true
        }
        return true
    })

    return (
        <div className="p-6">
            {/* Welcome Header */}
            <div className="mb-8">
                <div className="flex items-center gap-3 mb-4">
                    <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl">
                        <PiShieldCheckDuotone className="text-2xl text-white" />
                    </div>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                            Welcome back{user?.email ? `, ${user.email.split('@')[0]}` : ''}!
                        </h1>
                        <p className="text-gray-600 dark:text-gray-400">
                            Your compliance management dashboard
                        </p>
                    </div>
                </div>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <Card className="p-6">
                    <div className="flex items-center gap-4">
                        <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
                            <PiChartBarDuotone className="text-2xl text-green-600 dark:text-green-400" />
                        </div>
                        <div>
                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">0</h3>
                            <p className="text-gray-600 dark:text-gray-400">Active Frameworks</p>
                        </div>
                    </div>
                </Card>

                <Card className="p-6">
                    <div className="flex items-center gap-4">
                        <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                            <PiRocketLaunchDuotone className="text-2xl text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">0%</h3>
                            <p className="text-gray-600 dark:text-gray-400">Completion Rate</p>
                        </div>
                    </div>
                </Card>

                <Card className="p-6">
                    <div className="flex items-center gap-4">
                        <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                            <PiUserCircleDuotone className="text-2xl text-purple-600 dark:text-purple-400" />
                        </div>
                        <div>
                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Ready</h3>
                            <p className="text-gray-600 dark:text-gray-400">System Status</p>
                        </div>
                    </div>
                </Card>
            </div>

            {/* Quick Links */}
            <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Quick Actions
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {availableLinks.map((link, index) => (
                        <Link key={index} href={link.href}>
                            <Card className="p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer group">
                                <div className="flex items-start gap-4">
                                    <div className={`p-3 rounded-lg ${link.bgColor} group-hover:scale-110 transition-transform duration-200`}>
                                        <div className={link.color}>
                                            {link.icon}
                                        </div>
                                    </div>
                                    <div className="flex-1">
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                            {link.title}
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-400 text-sm">
                                            {link.description}
                                        </p>
                                    </div>
                                </div>
                            </Card>
                        </Link>
                    ))}
                </div>
            </div>

            {/* Getting Started */}
            <Card className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Getting Started
                </h2>
                <div className="space-y-4">
                    <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <span className="text-xs font-semibold text-blue-600 dark:text-blue-400">1</span>
                        </div>
                        <div>
                            <h4 className="font-medium text-gray-900 dark:text-white">Set up your first framework</h4>
                            <p className="text-gray-600 dark:text-gray-400 text-sm">
                                Navigate to Framework Management to configure PCI DSS or other compliance frameworks.
                            </p>
                        </div>
                    </div>
                    <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-gray-100 dark:bg-gray-900/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">2</span>
                        </div>
                        <div>
                            <h4 className="font-medium text-gray-900 dark:text-white">Configure system settings</h4>
                            <p className="text-gray-600 dark:text-gray-400 text-sm">
                                Use Admin Settings to customize your compliance management experience.
                            </p>
                        </div>
                    </div>
                </div>
            </Card>
        </div>
    )
}

export default ComplianceDashboardPage
