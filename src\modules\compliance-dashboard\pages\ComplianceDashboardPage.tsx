'use client'

import { useMemo } from 'react'
import MetricsGrid from '../components/MetricsGrid'
import { DashboardMetrics } from '../types'
import { useMockFrameworks } from '@/modules/framework-management/hooks'
import { useMockTasks } from '@/modules/task-engine/hooks'
import { useMockRisks } from '@/modules/risk-management/hooks'
import { getRiskLevel } from '@/shared/utils/compliance'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { StatusBadge } from '@/shared/components'
import {
    HiOutlineChevronRight,
    HiOutlineExclamationCircle,
    HiOutlineClock,
    HiOutlineCheckCircle,
} from 'react-icons/hi'

const ComplianceDashboardPage = () => {
    const { data: frameworks, loading: frameworksLoading } = useMockFrameworks()
    const { data: tasks, loading: tasksLoading } = useMockTasks()
    const { data: risks, loading: risksLoading } = useMockRisks()

    const loading = frameworksLoading || tasksLoading || risksLoading

    // Calculate dashboard metrics
    const metrics: DashboardMetrics = useMemo(() => {
        if (loading) {
            return {
                frameworks: {
                    total: 0,
                    active: 0,
                    inProgress: 0,
                    notStarted: 0,
                    averageProgress: 0,
                },
                controls: {
                    total: 0,
                    compliant: 0,
                    nonCompliant: 0,
                    inProgress: 0,
                    notStarted: 0,
                    needsReview: 0,
                    completionRate: 0,
                },
                tasks: {
                    total: 0,
                    open: 0,
                    inProgress: 0,
                    completed: 0,
                    overdue: 0,
                    dueThisWeek: 0,
                },
                risks: {
                    total: 0,
                    critical: 0,
                    high: 0,
                    medium: 0,
                    low: 0,
                    unmitigated: 0,
                },
                evidence: {
                    total: 0,
                    approved: 0,
                    pending: 0,
                    expired: 0,
                    readinessRate: 0,
                },
            }
        }

        // Framework metrics
        const frameworkMetrics = {
            total: frameworks.length,
            active: frameworks.filter((f) => f.status === 'Active').length,
            inProgress: frameworks.filter((f) => f.status === 'In Progress')
                .length,
            notStarted: frameworks.filter((f) => f.status === 'Not Started')
                .length,
            averageProgress:
                frameworks.length > 0
                    ? Math.round(
                          frameworks.reduce((acc, f) => acc + f.progress, 0) /
                              frameworks.length,
                      )
                    : 0,
        }

        // Task metrics
        const overdueTasks = tasks.filter((task) => {
            // Since TaskDetail extends Task, we can safely check if it's overdue
            if (task.status === 'Completed' || task.status === 'Cancelled')
                return false
            return new Date(task.dueDate) < new Date()
        })
        const now = new Date()
        const oneWeekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
        const dueThisWeek = tasks.filter((task) => {
            const dueDate = new Date(task.dueDate)
            return dueDate >= now && dueDate <= oneWeekFromNow
        })

        const taskMetrics = {
            total: tasks.length,
            open: tasks.filter((t) => t.status === 'Open').length,
            inProgress: tasks.filter((t) => t.status === 'In Progress').length,
            completed: tasks.filter((t) => t.status === 'Completed').length,
            overdue: overdueTasks.length,
            dueThisWeek: dueThisWeek.length,
        }

        // Risk metrics
        const risksByLevel = risks.reduce(
            (acc, risk) => {
                const level = getRiskLevel(risk.riskScore)
                acc[level.toLowerCase() as keyof typeof acc]++
                return acc
            },
            { critical: 0, high: 0, medium: 0, low: 0 },
        )

        const riskMetrics = {
            total: risks.length,
            critical: risksByLevel.critical,
            high: risksByLevel.high,
            medium: risksByLevel.medium,
            low: risksByLevel.low,
            unmitigated: risks.filter(
                (r) => r.status === 'Identified' || r.status === 'Assessed',
            ).length,
        }

        // Mock control and evidence metrics (would come from actual data)
        const controlMetrics = {
            total: frameworks.reduce((acc, f) => acc + f.controlCount, 0),
            compliant: Math.floor(
                frameworks.reduce((acc, f) => acc + f.controlCount, 0) * 0.65,
            ),
            nonCompliant: Math.floor(
                frameworks.reduce((acc, f) => acc + f.controlCount, 0) * 0.15,
            ),
            inProgress: Math.floor(
                frameworks.reduce((acc, f) => acc + f.controlCount, 0) * 0.15,
            ),
            notStarted: Math.floor(
                frameworks.reduce((acc, f) => acc + f.controlCount, 0) * 0.05,
            ),
            needsReview: Math.floor(
                frameworks.reduce((acc, f) => acc + f.controlCount, 0) * 0.08,
            ),
            completionRate: 65,
        }

        const evidenceMetrics = {
            total: 156,
            approved: 98,
            pending: 42,
            expired: 16,
            readinessRate: 63,
        }

        return {
            frameworks: frameworkMetrics,
            controls: controlMetrics,
            tasks: taskMetrics,
            risks: riskMetrics,
            evidence: evidenceMetrics,
        }
    }, [frameworks, tasks, risks, loading])

    // Recent activity (mock data)
    const recentActivity = [
        {
            id: '1',
            type: 'task_completed' as const,
            title: 'Quarterly PCI DSS Network Scan',
            description: 'Completed vulnerability scan for Q1 2024',
            user: 'John Doe',
            timestamp: '2024-01-15T10:30:00Z',
        },
        {
            id: '2',
            type: 'control_updated' as const,
            title: 'PCI DSS 3.4.1 - Encryption Implementation',
            description: 'Updated control status to Compliant',
            user: 'Jane Smith',
            timestamp: '2024-01-14T16:45:00Z',
        },
        {
            id: '3',
            type: 'risk_identified' as const,
            title: 'Third-Party Vendor Data Breach Risk',
            description: 'New high-risk item identified for payment processor',
            user: 'Mike Johnson',
            timestamp: '2024-01-14T09:15:00Z',
        },
    ]

    // Alerts (mock data)
    const alerts = [
        {
            id: '1',
            type: 'overdue_task' as const,
            severity: 'high' as const,
            title: 'Monthly Access Review Overdue',
            description: 'Access review task is 5 days overdue',
            entityType: 'task' as const,
        },
        {
            id: '2',
            type: 'high_risk' as const,
            severity: 'critical' as const,
            title: 'Critical Risk Identified',
            description:
                'Third-party vendor breach risk requires immediate attention',
            entityType: 'risk' as const,
        },
        {
            id: '3',
            type: 'expired_evidence' as const,
            severity: 'medium' as const,
            title: 'SOC 2 Certificate Expired',
            description: 'Vendor SOC 2 certificate expired last week',
            entityType: 'evidence' as const,
        },
    ]

    return (
        <div className="p-6 space-y-6">
            {/* Header */}
            <div>
                <h1 className="text-2xl font-bold">Compliance Dashboard</h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                    High-level insights across all compliance modules
                </p>
            </div>

            {/* Metrics Grid */}
            <MetricsGrid metrics={metrics} loading={loading} />

            {/* Alerts and Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Alerts */}
                <Card>
                    <div className="p-6">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="font-semibold">
                                Alerts & Notifications
                            </h3>
                            <Button
                                variant="plain"
                                size="sm"
                                icon={<HiOutlineChevronRight />}
                            >
                                View All
                            </Button>
                        </div>
                        <div className="space-y-3">
                            {alerts.map((alert) => (
                                <div
                                    key={alert.id}
                                    className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                                >
                                    <HiOutlineExclamationCircle
                                        className={`w-5 h-5 mt-0.5 ${
                                            alert.severity === 'critical'
                                                ? 'text-red-500'
                                                : alert.severity === 'high'
                                                  ? 'text-amber-500'
                                                  : 'text-blue-500'
                                        }`}
                                    />
                                    <div className="flex-1 min-w-0">
                                        <h4 className="font-medium text-sm">
                                            {alert.title}
                                        </h4>
                                        <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                            {alert.description}
                                        </p>
                                    </div>
                                    <StatusBadge
                                        status={alert.severity}
                                        size="sm"
                                    />
                                </div>
                            ))}
                        </div>
                    </div>
                </Card>

                {/* Recent Activity */}
                <Card>
                    <div className="p-6">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="font-semibold">Recent Activity</h3>
                            <Button
                                variant="plain"
                                size="sm"
                                icon={<HiOutlineChevronRight />}
                            >
                                View All
                            </Button>
                        </div>
                        <div className="space-y-3">
                            {recentActivity.map((activity) => (
                                <div
                                    key={activity.id}
                                    className="flex items-start gap-3"
                                >
                                    <div
                                        className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                            activity.type === 'task_completed'
                                                ? 'bg-green-100 text-green-600'
                                                : activity.type ===
                                                    'control_updated'
                                                  ? 'bg-blue-100 text-blue-600'
                                                  : 'bg-amber-100 text-amber-600'
                                        }`}
                                    >
                                        {activity.type === 'task_completed' ? (
                                            <HiOutlineCheckCircle className="w-4 h-4" />
                                        ) : activity.type ===
                                          'control_updated' ? (
                                            <HiOutlineClock className="w-4 h-4" />
                                        ) : (
                                            <HiOutlineExclamationCircle className="w-4 h-4" />
                                        )}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <h4 className="font-medium text-sm">
                                            {activity.title}
                                        </h4>
                                        <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                            {activity.description}
                                        </p>
                                        <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
                                            <span>{activity.user}</span>
                                            <span>•</span>
                                            <span>
                                                {new Date(
                                                    activity.timestamp,
                                                ).toLocaleDateString()}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </Card>
            </div>

            {/* Quick Actions */}
            <Card>
                <div className="p-6">
                    <h3 className="font-semibold mb-4">Quick Actions</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <Button variant="default" className="h-20 flex-col">
                            <HiOutlineCheckCircle className="w-6 h-6 mb-2" />
                            <span className="text-sm">Add Task</span>
                        </Button>
                        <Button variant="default" className="h-20 flex-col">
                            <HiOutlineExclamationCircle className="w-6 h-6 mb-2" />
                            <span className="text-sm">Report Risk</span>
                        </Button>
                        <Button variant="default" className="h-20 flex-col">
                            <HiOutlineClock className="w-6 h-6 mb-2" />
                            <span className="text-sm">Update Control</span>
                        </Button>
                        <Button variant="default" className="h-20 flex-col">
                            <HiOutlineChevronRight className="w-6 h-6 mb-2" />
                            <span className="text-sm">View Reports</span>
                        </Button>
                    </div>
                </div>
            </Card>
        </div>
    )
}

export default ComplianceDashboardPage
