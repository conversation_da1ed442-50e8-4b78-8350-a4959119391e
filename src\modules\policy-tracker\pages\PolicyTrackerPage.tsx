'use client'

import { useState } from 'react'
import { PolicyCard } from '../components'
import { useMockPolicies, usePolicyExport } from '../hooks'
import { Policy } from '@/shared/types/compliance'
import { FilterBar } from '@/shared/components'
import { POLICY_STATUSES } from '@/shared/constants/compliance'
import Button from '@/components/ui/Button'
import Tabs from '@/components/ui/Tabs'
import { HiPlus, HiOutlineDownload } from 'react-icons/hi'

const PolicyTrackerPage = () => {
    const { data: policies, loading, error } = useMockPolicies()
    const { exportPoliciesToCSV } = usePolicyExport()
    const [activeTab, setActiveTab] = useState('all')

    const filterOptions = {
        status: POLICY_STATUSES.map((status) => ({
            value: status,
            label: status,
        })),
        category: [
            { value: 'Security', label: 'Security' },
            { value: 'Privacy', label: 'Privacy' },
            { value: 'HR', label: 'HR' },
            { value: 'IT', label: 'IT' },
            { value: 'Finance', label: 'Finance' },
            { value: 'Operations', label: 'Operations' },
            { value: 'Compliance', label: 'Compliance' },
        ],
    }

    // Calculate stats
    const stats = {
        total: policies.length,
        published: policies.filter((p) => p.status === 'Published').length,
        draft: policies.filter((p) => p.status === 'Draft').length,
        reviewDue: policies.filter(
            (p) => p.nextReviewDate && new Date(p.nextReviewDate) <= new Date(),
        ).length,
    }

    const handleViewPolicy = (policy: Policy) => {
        console.log('View policy:', policy)
        // TODO: Navigate to policy detail page or open modal
    }

    const handleEditPolicy = (policy: Policy) => {
        console.log('Edit policy:', policy)
        // TODO: Open edit modal or navigate to edit page
    }

    const handleViewControls = (policy: Policy) => {
        console.log('View controls for policy:', policy)
        // TODO: Navigate to control hub with policy filter
    }

    const handleViewAcknowledgments = (policy: Policy) => {
        console.log('View acknowledgments for policy:', policy)
        // TODO: Open acknowledgments modal
    }

    const handleAddPolicy = () => {
        console.log('Add new policy')
        // TODO: Open add policy modal or navigate to add page
    }

    const handleExportCSV = () => {
        exportPoliciesToCSV(policies)
    }

    if (error) {
        return (
            <div className="p-6">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h3 className="text-red-800 font-medium">
                        Error loading policies
                    </h3>
                    <p className="text-red-600 text-sm mt-1">{error}</p>
                </div>
            </div>
        )
    }

    return (
        <div className="p-6 space-y-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 className="text-2xl font-bold">Policy Tracker</h1>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">
                        Store, version, and manage organizational policies
                    </p>
                </div>
                <div className="flex gap-2">
                    <Button
                        variant="default"
                        icon={<HiOutlineDownload />}
                        onClick={handleExportCSV}
                    >
                        Export CSV
                    </Button>
                    <Button
                        variant="solid"
                        icon={<HiPlus />}
                        onClick={handleAddPolicy}
                        className="bg-emerald-500 hover:bg-emerald-600"
                    >
                        Add Policy
                    </Button>
                </div>
            </div>

            {/* Tabs */}
            <Tabs
                value={activeTab}
                onChange={(val) => setActiveTab(val as string)}
            >
                <Tabs.TabList>
                    <Tabs.TabNav value="all">
                        All Policies ({stats.total})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="published">
                        Published ({stats.published})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="draft">
                        Draft ({stats.draft})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="review-due">
                        Review Due ({stats.reviewDue})
                    </Tabs.TabNav>
                </Tabs.TabList>
            </Tabs>

            {/* Filters */}
            <FilterBar
                searchPlaceholder="Search policies..."
                filters={filterOptions}
                onFilterChange={() => {}}
            />

            {/* Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="text-2xl font-bold text-blue-600">
                        {stats.total}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        Total Policies
                    </div>
                </div>
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="text-2xl font-bold text-emerald-600">
                        {stats.published}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        Published
                    </div>
                </div>
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="text-2xl font-bold text-amber-600">
                        {stats.draft}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        Draft
                    </div>
                </div>
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="text-2xl font-bold text-red-600">
                        {stats.reviewDue}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        Review Due
                    </div>
                </div>
            </div>

            {/* Policy Grid */}
            {loading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {Array.from({ length: 6 }).map((_, i) => (
                        <div
                            key={i}
                            className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"
                        ></div>
                    ))}
                </div>
            ) : policies.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {policies.map((policy) => (
                        <PolicyCard
                            key={policy.id}
                            policy={policy}
                            onView={handleViewPolicy}
                            onEdit={handleEditPolicy}
                            onViewControls={handleViewControls}
                            onViewAcknowledgments={handleViewAcknowledgments}
                        />
                    ))}
                </div>
            ) : (
                <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                        <svg
                            className="w-16 h-16 mx-auto"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={1}
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                        </svg>
                    </div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                        No policies found
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                        Get started by creating your first organizational
                        policy.
                    </p>
                    <Button
                        variant="solid"
                        icon={<HiPlus />}
                        onClick={handleAddPolicy}
                        className="bg-emerald-500 hover:bg-emerald-600"
                    >
                        Add Policy
                    </Button>
                </div>
            )}
        </div>
    )
}

export default PolicyTrackerPage
