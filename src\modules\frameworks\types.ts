// Import types first
import type {
    Framework,
    Control,
    Task,
    Risk,
    Policy,
} from '@/shared/types/compliance'

// Framework Management module types
export interface FrameworkDetail extends Framework {
    controls: Control[]
    tasks: Task[]
    risks: Risk[]
    policies: Policy[]
    lastAuditDate?: string
    nextAuditDate?: string
    certificationStatus?:
        | 'Not Certified'
        | 'In Progress'
        | 'Certified'
        | 'Expired'
    certificationExpiry?: string
    auditFirm?: string
    complianceOfficer?: string
}

export interface FrameworkTemplate {
    id: string
    name: string
    version: string
    category: string
    description: string
    controlCount: number
    requirements: FrameworkRequirement[]
    isOfficial: boolean
    source: string
}

export interface FrameworkRequirement {
    id: string
    number: string
    title: string
    description: string
    category: string
    subcategory?: string
    controls: ControlTemplate[]
}

export interface ControlTemplate {
    id: string
    number: string
    title: string
    description: string
    requirements: string[]
    riskLevel: 'Low' | 'Medium' | 'High' | 'Critical'
    implementationGuidance?: string
    testingProcedures?: string[]
    evidenceRequirements?: string[]
}

// Re-export shared types for convenience
export type { Framework, Control, Task, Risk, Policy }
