'use client'

import { FrameworkList } from '../components'
import { useMockFrameworks } from '../hooks'
import { Framework } from '../types'

const FrameworkManagementPage = () => {
    const { data: frameworks, loading, error } = useMockFrameworks()

    const handleViewFramework = (framework: Framework) => {
        console.log('View framework:', framework)
        // TODO: Navigate to framework detail page or open modal
    }

    const handleEditFramework = (framework: Framework) => {
        console.log('Edit framework:', framework)
        // TODO: Open edit modal or navigate to edit page
    }

    const handleManageControls = (framework: Framework) => {
        console.log('Manage controls for framework:', framework)
        // TODO: Navigate to control hub with framework filter
    }

    const handleViewReports = (framework: Framework) => {
        console.log('View reports for framework:', framework)
        // TODO: Navigate to reports page with framework filter
    }

    const handleAddFramework = () => {
        console.log('Add new framework')
        // TODO: Open add framework modal or navigate to add page
    }

    if (error) {
        return (
            <div className="p-6">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h3 className="text-red-800 font-medium">
                        Error loading frameworks
                    </h3>
                    <p className="text-red-600 text-sm mt-1">{error}</p>
                </div>
            </div>
        )
    }

    return (
        <div className="p-6">
            <FrameworkList
                frameworks={frameworks}
                loading={loading}
                onView={handleViewFramework}
                onEdit={handleEditFramework}
                onManageControls={handleManageControls}
                onViewReports={handleViewReports}
                onAddFramework={handleAddFramework}
            />
        </div>
    )
}

export default FrameworkManagementPage
