'use client'

import { useState, useMemo } from 'react'
import { FilterBar } from '@/shared/components'
import { FilterOptions } from '@/shared/types/compliance'
import {
    RISK_CATEGORIES,
    RISK_STATUSES,
    RISK_LEVELS,
} from '@/shared/constants/compliance'
import RiskCard from './RiskCard'
import { Risk } from '@/shared/types/compliance'
import Button from '@/components/ui/Button'
import Tabs from '@/components/ui/Tabs'
import {
    HiPlus,
    HiOutlineDownload,
    HiOutlineViewGrid,
    HiOutlineViewList,
} from 'react-icons/hi'
import { getRiskLevel } from '@/shared/utils/compliance'

interface RiskListProps {
    risks: Risk[]
    loading?: boolean
    onView?: (risk: Risk) => void
    onEdit?: (risk: Risk) => void
    onViewControls?: (risk: Risk) => void
    onUpdateStatus?: (risk: Risk) => void
    onAddRisk?: () => void
    onExportCSV?: () => void
}

const RiskList = ({
    risks,
    loading = false,
    onView,
    onEdit,
    onViewControls,
    onUpdateStatus,
    onAddRisk,
    onExportCSV,
}: RiskListProps) => {
    const [filters, setFilters] = useState<FilterOptions>({})
    const [activeTab, setActiveTab] = useState('all')
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

    // Filter options for the FilterBar
    const filterOptions = {
        status: RISK_STATUSES.map((status) => ({
            value: status,
            label: status,
        })),
        category: RISK_CATEGORIES.map((category) => ({
            value: category,
            label: category,
        })),
        priority: RISK_LEVELS.map((level) => ({ value: level, label: level })),
        owner: [
            { value: 'security-team', label: 'Security Team' },
            { value: 'hr-team', label: 'HR Team' },
            { value: 'network-team', label: 'Network Team' },
            { value: 'vendor-management', label: 'Vendor Management' },
        ],
    }

    // Filter risks based on current filters and active tab
    const filteredRisks = useMemo(() => {
        let filtered = risks

        // Apply tab filter first
        switch (activeTab) {
            case 'high-critical':
                filtered = filtered.filter((risk) => {
                    const level = getRiskLevel(risk.riskScore)
                    return level === 'High' || level === 'Critical'
                })
                break
            case 'unmitigated':
                filtered = filtered.filter(
                    (risk) =>
                        risk.status === 'Identified' ||
                        risk.status === 'Assessed',
                )
                break
            case 'overdue':
                filtered = filtered.filter(
                    (risk) =>
                        risk.mitigationDeadline &&
                        new Date(risk.mitigationDeadline) < new Date(),
                )
                break
            case 'review-due':
                filtered = filtered.filter(
                    (risk) =>
                        risk.nextReviewDate &&
                        new Date(risk.nextReviewDate) <= new Date(),
                )
                break
            default:
                // 'all' - no additional filtering
                break
        }

        // Apply search and other filters
        return filtered.filter((risk) => {
            // Search filter
            if (filters.search) {
                const searchTerm = filters.search.toLowerCase()
                const matchesSearch =
                    risk.title.toLowerCase().includes(searchTerm) ||
                    risk.description.toLowerCase().includes(searchTerm) ||
                    risk.category.toLowerCase().includes(searchTerm) ||
                    risk.tags.some((tag) =>
                        tag.toLowerCase().includes(searchTerm),
                    )

                if (!matchesSearch) return false
            }

            // Status filter
            if (filters.status && filters.status.length > 0) {
                if (!filters.status.includes(risk.status)) return false
            }

            // Category filter
            if (filters.category && filters.category.length > 0) {
                if (!filters.category.includes(risk.category)) return false
            }

            // Priority (risk level) filter
            if (filters.priority && filters.priority.length > 0) {
                const riskLevel = getRiskLevel(risk.riskScore)
                if (!filters.priority.includes(riskLevel)) return false
            }

            // Owner filter
            if (filters.owner && filters.owner.length > 0) {
                if (!risk.owner || !filters.owner.includes(risk.owner))
                    return false
            }

            return true
        })
    }, [risks, filters, activeTab])

    // Calculate stats
    const stats = useMemo(() => {
        const highCriticalRisks = risks.filter((risk) => {
            const level = getRiskLevel(risk.riskScore)
            return level === 'High' || level === 'Critical'
        })
        const unmitigatedRisks = risks.filter(
            (risk) =>
                risk.status === 'Identified' || risk.status === 'Assessed',
        )
        const overdueRisks = risks.filter(
            (risk) =>
                risk.mitigationDeadline &&
                new Date(risk.mitigationDeadline) < new Date(),
        )
        const reviewDueRisks = risks.filter(
            (risk) =>
                risk.nextReviewDate &&
                new Date(risk.nextReviewDate) <= new Date(),
        )

        return {
            total: risks.length,
            highCritical: highCriticalRisks.length,
            unmitigated: unmitigatedRisks.length,
            overdue: overdueRisks.length,
            reviewDue: reviewDueRisks.length,
        }
    }, [risks])

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="animate-pulse">
                    <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4"></div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {Array.from({ length: 6 }).map((_, i) => (
                            <div
                                key={i}
                                className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg"
                            ></div>
                        ))}
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 className="text-2xl font-bold">Risk Management</h1>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">
                        Track and manage business and security risks
                    </p>
                </div>
                <div className="flex gap-2">
                    <Button
                        variant="default"
                        icon={<HiOutlineDownload />}
                        onClick={onExportCSV}
                    >
                        Export CSV
                    </Button>
                    <Button
                        variant="solid"
                        icon={<HiPlus />}
                        onClick={onAddRisk}
                        className="bg-emerald-500 hover:bg-emerald-600"
                    >
                        Add Risk
                    </Button>
                </div>
            </div>

            {/* Tabs */}
            <Tabs
                value={activeTab}
                onChange={(val) => setActiveTab(val as string)}
            >
                <Tabs.TabList>
                    <Tabs.TabNav value="all">
                        All Risks ({stats.total})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="high-critical">
                        High/Critical ({stats.highCritical})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="unmitigated">
                        Unmitigated ({stats.unmitigated})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="overdue">
                        Overdue ({stats.overdue})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="review-due">
                        Review Due ({stats.reviewDue})
                    </Tabs.TabNav>
                </Tabs.TabList>
            </Tabs>

            {/* Filters and View Toggle */}
            <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                    <FilterBar
                        searchPlaceholder="Search risks..."
                        filters={filterOptions}
                        onFilterChange={setFilters}
                    />
                </div>
                <div className="flex gap-2">
                    <Button
                        variant={viewMode === 'grid' ? 'solid' : 'default'}
                        icon={<HiOutlineViewGrid />}
                        onClick={() => setViewMode('grid')}
                        size="sm"
                    />
                    <Button
                        variant={viewMode === 'list' ? 'solid' : 'default'}
                        icon={<HiOutlineViewList />}
                        onClick={() => setViewMode('list')}
                        size="sm"
                    />
                </div>
            </div>

            {/* Risk Display */}
            {filteredRisks.length > 0 ? (
                <div
                    className={
                        viewMode === 'grid'
                            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                            : 'space-y-3'
                    }
                >
                    {filteredRisks.map((risk) => (
                        <RiskCard
                            key={risk.id}
                            risk={risk}
                            compact={viewMode === 'list'}
                            onView={onView}
                            onEdit={onEdit}
                            onViewControls={onViewControls}
                            onUpdateStatus={onUpdateStatus}
                        />
                    ))}
                </div>
            ) : (
                <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                        <svg
                            className="w-16 h-16 mx-auto"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={1}
                                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                            />
                        </svg>
                    </div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                        No risks found
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                        {Object.keys(filters).length > 0 || activeTab !== 'all'
                            ? 'Try adjusting your filters or tab selection to find what you need.'
                            : 'Get started by identifying your first business or security risk.'}
                    </p>
                    <Button
                        variant="solid"
                        icon={<HiPlus />}
                        onClick={onAddRisk}
                        className="bg-emerald-500 hover:bg-emerald-600"
                    >
                        Add Risk
                    </Button>
                </div>
            )}
        </div>
    )
}

export default RiskList
