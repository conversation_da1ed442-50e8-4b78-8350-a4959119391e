'use server'
import type { SignInCredential } from '@/@types/auth'
import sleep from '@/utils/sleep'

// Mock user data for demo purposes
const mockUsers = [
    {
        id: '21',
        avatar: '',
        userName: '<PERSON>',
        email: '<EMAIL>',
        authority: ['admin', 'user'],
        password: '123Qwe',
        accountUserName: 'admin',
    },
]

const validateCredential = async (values: SignInCredential) => {
    /** Implement your validation here, as this is just a mock */
    const { email, password } = values

    await sleep(80)

    const user = mockUsers.find(
        (user) => user.email === email && user.password === password,
    )

    return user
}

export default validateCredential
