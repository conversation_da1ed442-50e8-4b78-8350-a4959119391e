'use client'

import { useCallback } from 'react'
import { useSupabaseMutation } from '@/shared/hooks'
import {
    User,
    CreateUserRequest,
    UpdateUserRequest,
    DEFAULT_USER_PERMISSIONS,
    DEFAULT_ADMIN_PERMISSIONS,
} from '../types'

export const useUserMutations = () => {
    const { create, update, remove, loading, error } =
        useSupabaseMutation<User>('users')

    const createUser = useCallback(
        async (userData: CreateUserRequest) => {
            const newUser: Partial<User> = {
                ...userData,
                id: crypto.randomUUID(),
                status: 'pending',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                organizationId: 'default-org', // This should come from auth context
                permissions:
                    userData.role === 'admin'
                        ? Object.keys(DEFAULT_ADMIN_PERMISSIONS)
                        : Object.keys(DEFAULT_USER_PERMISSIONS).filter(
                              (key) =>
                                  DEFAULT_USER_PERMISSIONS[
                                      key as keyof typeof DEFAULT_USER_PERMISSIONS
                                  ],
                          ),
            }

            return await create(newUser)
        },
        [create],
    )

    const updateUser = useCallback(
        async (id: string, updates: UpdateUserRequest) => {
            const updatedData = {
                ...updates,
                updatedAt: new Date().toISOString(),
            }

            // If role is being changed, update permissions accordingly
            if (updates.role) {
                updatedData.permissions =
                    updates.role === 'admin'
                        ? Object.keys(DEFAULT_ADMIN_PERMISSIONS)
                        : Object.keys(DEFAULT_USER_PERMISSIONS).filter(
                              (key) =>
                                  DEFAULT_USER_PERMISSIONS[
                                      key as keyof typeof DEFAULT_USER_PERMISSIONS
                                  ],
                          )
            }

            return await update(id, updatedData)
        },
        [update],
    )

    const deleteUser = useCallback(
        async (id: string) => {
            return await remove(id)
        },
        [remove],
    )

    const activateUser = useCallback(
        async (id: string) => {
            return await updateUser(id, { status: 'active' })
        },
        [updateUser],
    )

    const deactivateUser = useCallback(
        async (id: string) => {
            return await updateUser(id, { status: 'inactive' })
        },
        [updateUser],
    )

    const suspendUser = useCallback(
        async (id: string) => {
            return await updateUser(id, { status: 'suspended' })
        },
        [updateUser],
    )

    const changeUserRole = useCallback(
        async (id: string, role: 'admin' | 'user') => {
            return await updateUser(id, { role })
        },
        [updateUser],
    )

    const sendUserInvitation = useCallback(
        async (email: string, role: 'admin' | 'user') => {
            // This would typically send an email invitation
            console.log(`Sending invitation to ${email} with role ${role}`)

            // For now, just create a pending user
            return await createUser({
                email,
                userName: email.split('@')[0],
                role,
                sendInvite: true,
            })
        },
        [createUser],
    )

    return {
        createUser,
        updateUser,
        deleteUser,
        activateUser,
        deactivateUser,
        suspendUser,
        changeUserRole,
        sendUserInvitation,
        loading,
        error,
    }
}
