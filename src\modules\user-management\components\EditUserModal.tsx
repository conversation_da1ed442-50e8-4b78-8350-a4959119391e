'use client'

import { useState, useEffect } from 'react'
import Dialog from '@/components/ui/Dialog'
import But<PERSON> from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Select from '@/components/ui/Select'
import FormItem from '@/components/ui/Form/FormItem'
import FormContainer from '@/components/ui/Form/FormContainer'
import { User, UpdateUserRequest } from '../types'
import { HiOutlineX } from 'react-icons/hi'

interface EditUserModalProps {
    isOpen: boolean
    onClose: () => void
    onSubmit: (id: string, userData: UpdateUserRequest) => Promise<void>
    user: User | null
    loading?: boolean
}

const EditUserModal = ({
    isOpen,
    onClose,
    onSubmit,
    user,
    loading = false,
}: EditUserModalProps) => {
    const [formData, setFormData] = useState<UpdateUserRequest>({
        userName: '',
        firstName: '',
        lastName: '',
        role: 'user',
        status: 'active',
    })

    const [errors, setErrors] = useState<Record<string, string>>({})

    useEffect(() => {
        if (user) {
            setFormData({
                userName: user.userName,
                firstName: user.firstName || '',
                lastName: user.lastName || '',
                role: user.role,
                status: user.status,
            })
        }
    }, [user])

    const validateForm = () => {
        const newErrors: Record<string, string> = {}

        if (!formData.userName) {
            newErrors.userName = 'Username is required'
        } else if (formData.userName.length < 3) {
            newErrors.userName = 'Username must be at least 3 characters'
        }

        if (!formData.firstName) {
            newErrors.firstName = 'First name is required'
        }

        if (!formData.lastName) {
            newErrors.lastName = 'Last name is required'
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!user || !validateForm()) {
            return
        }

        try {
            await onSubmit(user.id, formData)
            handleClose()
        } catch (error) {
            console.error('Error updating user:', error)
        }
    }

    const handleClose = () => {
        setFormData({
            userName: '',
            firstName: '',
            lastName: '',
            role: 'user',
            status: 'active',
        })
        setErrors({})
        onClose()
    }

    const handleInputChange = (
        field: keyof UpdateUserRequest,
        value: string,
    ) => {
        setFormData((prev) => ({ ...prev, [field]: value }))

        // Clear error when user starts typing
        if (errors[field]) {
            setErrors((prev) => ({ ...prev, [field]: '' }))
        }
    }

    const roleOptions = [
        { value: 'user', label: 'User' },
        { value: 'admin', label: 'Administrator' },
    ]

    const statusOptions = [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' },
        { value: 'suspended', label: 'Suspended' },
    ]

    return (
        <Dialog isOpen={isOpen} onClose={handleClose} width={500}>
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold">Edit User</h3>
                <Button
                    variant="plain"
                    size="sm"
                    icon={<HiOutlineX />}
                    onClick={handleClose}
                />
            </div>

            <form onSubmit={handleSubmit}>
                <div className="p-6 space-y-4">
                    <FormContainer>
                        <FormItem
                            label="Username"
                            invalid={!!errors.userName}
                            errorMessage={errors.userName}
                        >
                            <Input
                                placeholder="username"
                                value={formData.userName}
                                onChange={(e) =>
                                    handleInputChange(
                                        'userName',
                                        e.target.value,
                                    )
                                }
                                invalid={!!errors.userName}
                            />
                        </FormItem>

                        <div className="grid grid-cols-2 gap-4">
                            <FormItem
                                label="First Name"
                                invalid={!!errors.firstName}
                                errorMessage={errors.firstName}
                            >
                                <Input
                                    placeholder="John"
                                    value={formData.firstName}
                                    onChange={(e) =>
                                        handleInputChange(
                                            'firstName',
                                            e.target.value,
                                        )
                                    }
                                    invalid={!!errors.firstName}
                                />
                            </FormItem>

                            <FormItem
                                label="Last Name"
                                invalid={!!errors.lastName}
                                errorMessage={errors.lastName}
                            >
                                <Input
                                    placeholder="Doe"
                                    value={formData.lastName}
                                    onChange={(e) =>
                                        handleInputChange(
                                            'lastName',
                                            e.target.value,
                                        )
                                    }
                                    invalid={!!errors.lastName}
                                />
                            </FormItem>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <FormItem label="Role">
                                <Select
                                    options={roleOptions}
                                    value={roleOptions.find(
                                        (option) =>
                                            option.value === formData.role,
                                    )}
                                    onChange={(option) =>
                                        handleInputChange(
                                            'role',
                                            option?.value || 'user',
                                        )
                                    }
                                />
                            </FormItem>

                            <FormItem label="Status">
                                <Select
                                    options={statusOptions}
                                    value={statusOptions.find(
                                        (option) =>
                                            option.value === formData.status,
                                    )}
                                    onChange={(option) =>
                                        handleInputChange(
                                            'status',
                                            option?.value || 'active',
                                        )
                                    }
                                />
                            </FormItem>
                        </div>
                    </FormContainer>
                </div>

                <div className="flex justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
                    <Button
                        type="button"
                        variant="plain"
                        onClick={handleClose}
                        disabled={loading}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="submit"
                        variant="solid"
                        loading={loading}
                        className="bg-emerald-500 hover:bg-emerald-600"
                    >
                        Update User
                    </Button>
                </div>
            </form>
        </Dialog>
    )
}

export default EditUserModal
