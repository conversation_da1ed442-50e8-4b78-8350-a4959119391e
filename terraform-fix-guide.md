# 🔧 **Terraform Configuration Fix Guide**

## Problem

You have duplicate AWS provider configurations in your Terraform files:

- `main.tf` line 1: `provider "aws" {`
- `billing_alert.tf` line 2: `provider "aws" {`

## Solution Options

### Option 1: Consolidate Providers (Recommended)

Keep the AWS provider in `main.tf` and remove it from `billing_alert.tf`:

**In `main.tf`:**

```hcl
# Configure the AWS Provider
provider "aws" {
  region = var.aws_region

  default_tags {
    tags = {
      Environment = var.environment
      Project     = "checkgap"
      ManagedBy   = "terraform"
    }
  }
}

# Add any other providers here
```

**In `billing_alert.tf`:**

```hcl
# Remove the provider "aws" block from this file
# Keep only the resources

resource "aws_cloudwatch_metric_alarm" "billing_alert" {
  # Your billing alert configuration
}

resource "aws_sns_topic" "billing_alerts" {
  # Your SNS topic configuration
}
```

### Option 2: Use Provider Aliases

If you need different AWS configurations, use aliases:

**In `main.tf`:**

```hcl
# Default AWS provider
provider "aws" {
  region = var.aws_region
}

# Billing-specific provider (if needed in different region)
provider "aws" {
  alias  = "billing"
  region = "us-east-1"  # CloudWatch billing metrics are only in us-east-1
}
```

**In `billing_alert.tf`:**

```hcl
# Use the aliased provider
resource "aws_cloudwatch_metric_alarm" "billing_alert" {
  provider = aws.billing
  # Your configuration
}
```

## Quick Fix Commands

1. **Navigate to your terraform directory:**

    ```bash
    cd D:\WORK\HUANDER\hProjects\checkgap-infra
    ```

2. **Edit billing_alert.tf to remove the provider block:**

    ```bash
    # Open billing_alert.tf and remove lines that look like:
    # provider "aws" {
    #   region = "us-east-1"
    # }
    ```

3. **Ensure main.tf has the provider:**

    ```bash
    # Make sure main.tf contains:
    # provider "aws" {
    #   region = var.aws_region
    # }
    ```

4. **Run terraform init again:**
    ```bash
    terraform init
    ```

## Example Fixed Structure

**main.tf:**

```hcl
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region

  default_tags {
    tags = {
      Environment = var.environment
      Project     = "checkgap"
      ManagedBy   = "terraform"
    }
  }
}
```

**variables.tf:**

```hcl
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "eu-west-2"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}
```

**billing_alert.tf:**

```hcl
# Billing alert resources (no provider block)
resource "aws_cloudwatch_metric_alarm" "billing_alert" {
  alarm_name          = "billing-alert-${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "EstimatedCharges"
  namespace           = "AWS/Billing"
  period              = "86400"
  statistic           = "Maximum"
  threshold           = "10"
  alarm_description   = "This metric monitors aws billing"
  alarm_actions       = [aws_sns_topic.billing_alerts.arn]

  dimensions = {
    Currency = "USD"
  }
}

resource "aws_sns_topic" "billing_alerts" {
  name = "billing-alerts-${var.environment}"
}
```

## After Fixing

Once you've removed the duplicate provider, you should be able to run:

```bash
terraform init
terraform plan
terraform apply
```

## Common Terraform Best Practices

1. **Single Provider Declaration**: Declare each provider only once in your configuration
2. **Use Variables**: Make your configuration flexible with variables
3. **Default Tags**: Use default tags for consistent resource tagging
4. **Version Constraints**: Pin provider versions for reproducibility
5. **State Management**: Use remote state for team collaboration

## Need Help?

If you're still having issues:

1. Share the contents of your `main.tf` and `billing_alert.tf` files
2. Check for any other `.tf` files that might have provider blocks
3. Ensure you're in the correct directory when running terraform commands
