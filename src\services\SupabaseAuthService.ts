import { createClient } from '@/lib/supabase/client'
import type { AuthError, User, Session } from '@supabase/supabase-js'

export interface AuthResponse {
    user: User | null
    error: AuthError | null
}

export interface SignInCredentials {
    email: string
    password: string
}

export interface SignUpCredentials {
    email: string
    password: string
    userName?: string
}

class SupabaseAuthService {
    private supabase = createClient()

    // Sign in with email and password
    async signInWithPassword(
        credentials: SignInCredentials,
    ): Promise<AuthResponse> {
        const { data, error } = await this.supabase.auth.signInWithPassword({
            email: credentials.email,
            password: credentials.password,
        })

        return {
            user: data.user,
            error,
        }
    }

    // Sign up with email and password
    async signUpWithPassword(
        credentials: SignUpCredentials,
    ): Promise<AuthResponse> {
        const { data, error } = await this.supabase.auth.signUp({
            email: credentials.email,
            password: credentials.password,
            options: {
                data: {
                    user_name: credentials.userName,
                },
            },
        })

        return {
            user: data.user,
            error,
        }
    }

    // Sign in with Google
    async signInWithGoogle(redirectTo?: string) {
        const { data, error } = await this.supabase.auth.signInWithOAuth({
            provider: 'google',
            options: {
                redirectTo: redirectTo || window.location.origin + '/hub',
            },
        })

        return { data, error }
    }

    // Sign in with GitHub
    async signInWithGitHub(redirectTo?: string) {
        const { data, error } = await this.supabase.auth.signInWithOAuth({
            provider: 'github',
            options: {
                redirectTo: redirectTo || window.location.origin + '/hub',
            },
        })

        return { data, error }
    }

    // Sign out
    async signOut() {
        const { error } = await this.supabase.auth.signOut()
        return { error }
    }

    // Get current user
    async getCurrentUser() {
        const {
            data: { user },
            error,
        } = await this.supabase.auth.getUser()
        return { user, error }
    }

    // Get current session
    async getCurrentSession() {
        const {
            data: { session },
            error,
        } = await this.supabase.auth.getSession()
        return { session, error }
    }

    // Listen to auth state changes
    onAuthStateChange(
        callback: (event: string, session: Session | null) => void,
    ) {
        return this.supabase.auth.onAuthStateChange(callback)
    }

    // Reset password
    async resetPassword(email: string) {
        const { data, error } = await this.supabase.auth.resetPasswordForEmail(
            email,
            {
                redirectTo: window.location.origin + '/reset-password',
            },
        )

        return { data, error }
    }

    // Update password
    async updatePassword(password: string) {
        const { data, error } = await this.supabase.auth.updateUser({
            password,
        })

        return { data, error }
    }
}

const supabaseAuthService = new SupabaseAuthService()
export default supabaseAuthService
