'use client'

import SignIn from '@/components/auth/SignIn'
import { useAuth } from '@/contexts/AuthContext'
import type {
    OnSignInPayload,
    OnOauthSignInPayload,
} from '@/components/auth/SignIn'

const SignInClient = () => {
    const { signIn, signInWithGoogle, signInWithGitHub } = useAuth()

    const handleSignIn = async ({
        values,
        setSubmitting,
        setMessage,
    }: OnSignInPayload) => {
        setSubmitting(true)

        try {
            const { error } = await signIn(values.email, values.password)
            if (error) {
                setMessage(error.message || 'Sign in failed')
            }
        } catch {
            setMessage('An unexpected error occurred')
        } finally {
            setSubmitting(false)
        }
    }

    const handleOAuthSignIn = async ({ type }: OnOauthSignInPayload) => {
        try {
            if (type === 'google') {
                await signInWithGoogle()
            }
            if (type === 'github') {
                await signInWithGitHub()
            }
        } catch (error) {
            console.error('OAuth sign in error:', error)
        }
    }

    return <SignIn onSignIn={handleSignIn} onOauthSignIn={handleOAuthSignIn} />
}

export default SignInClient
