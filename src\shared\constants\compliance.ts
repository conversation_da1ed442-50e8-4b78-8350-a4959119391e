// Compliance-related constants

export const FRAMEWORK_CATEGORIES = [
    'Cybersecurity',
    'Privacy',
    'Financial',
    'Healthcare',
    'Environmental',
    'Quality',
    'Strategic',
    'AI Ethics',
] as const

export const FRAMEWORK_REGIONS = ['Global', 'US', 'EU', 'Internal'] as const

export const FRAMEWORK_STATUSES = [
    'Active',
    'In Progress',
    'Not Started',
    'Archived',
] as const

export const CONTROL_STATUSES = [
    'Not Started',
    'In Progress',
    'Implemented',
    'Needs Review',
    'Compliant',
    'Non-Compliant',
] as const

export const EVIDENCE_READINESS = [
    'Not Ready',
    'Partial',
    'Ready',
    'Approved',
] as const

export const RISK_LEVELS = ['Low', 'Medium', 'High', 'Critical'] as const

export const TASK_TYPES = ['One-time', 'Recurring'] as const

export const TASK_FREQUENCIES = [
    'Daily',
    'Weekly',
    'Monthly',
    'Quarterly',
    'Annually',
] as const

export const TASK_STATUSES = [
    'Open',
    'In Progress',
    'Completed',
    'Overdue',
    'Cancelled',
] as const

export const PRIORITY_LEVELS = ['Low', 'Medium', 'High', 'Critical'] as const

export const RISK_CATEGORIES = [
    'Operational',
    'Financial',
    'Strategic',
    'Compliance',
    'Reputational',
    'Technology',
] as const

export const RISK_STATUSES = [
    'Identified',
    'Assessed',
    'Mitigated',
    'Accepted',
    'Transferred',
    'Avoided',
] as const

export const VENDOR_CATEGORIES = [
    'Technology',
    'Professional Services',
    'Infrastructure',
    'Financial',
    'Other',
] as const

export const VENDOR_STATUSES = [
    'Active',
    'Inactive',
    'Under Review',
    'Terminated',
] as const

export const DOCUMENT_TYPES = [
    'Contract',
    'SOC 2',
    'ISO 27001',
    'Security Assessment',
    'Insurance',
    'Other',
] as const

export const POLICY_STATUSES = [
    'Draft',
    'Under Review',
    'Approved',
    'Published',
    'Archived',
] as const

export const ASSET_TYPES = [
    'System',
    'Application',
    'Database',
    'Network',
    'Physical',
    'Cloud Service',
    'Other',
] as const

export const ASSET_CLASSIFICATIONS = [
    'Public',
    'Internal',
    'Confidential',
    'Restricted',
] as const

export const ASSET_STATUSES = [
    'Active',
    'Inactive',
    'Decommissioned',
    'Under Review',
] as const

export const EVIDENCE_TYPES = [
    'Screenshot',
    'Document',
    'Log File',
    'Certificate',
    'Report',
    'Other',
] as const

export const EVIDENCE_STATUSES = [
    'Pending',
    'Approved',
    'Rejected',
    'Expired',
] as const

// PCI DSS 4.0.1 specific constants
export const PCI_DSS_REQUIREMENTS = [
    {
        id: '1',
        title: 'Install and maintain network security controls',
        description:
            'Firewalls and routers are key components of the architecture that controls entry to and exit from the network',
    },
    {
        id: '2',
        title: 'Apply secure configurations to all system components',
        description:
            'Malicious individuals, both external and internal to an entity, often use default passwords and other vendor default settings to compromise systems',
    },
    {
        id: '3',
        title: 'Protect stored account data',
        description:
            'Protection methods such as encryption, truncation, masking, and hashing are critical components of account data protection',
    },
    {
        id: '4',
        title: 'Protect account data with strong cryptography during transmission over open, public networks',
        description:
            'Sensitive information must be encrypted during transmission over networks that are easy to intercept',
    },
    {
        id: '5',
        title: 'Protect all systems and networks from malicious software',
        description:
            "Malicious software (malware) is software or firmware designed to infiltrate or damage a computer system without the owner's knowledge or consent",
    },
    {
        id: '6',
        title: 'Develop and maintain secure systems and software',
        description:
            'Security vulnerabilities in systems and software may allow criminals to access PAN and related data',
    },
    {
        id: '7',
        title: 'Restrict access to system components and cardholder data by business need to know',
        description:
            'To ensure critical data can only be accessed by authorized personnel, systems and processes must be in place to limit access based on need to know',
    },
    {
        id: '8',
        title: 'Identify users and authenticate access to system components',
        description:
            'Assigning a unique identification (ID) to each person with access ensures that each individual is uniquely accountable for their actions',
    },
    {
        id: '9',
        title: 'Restrict physical access to cardholder data',
        description:
            'Any physical access to data or systems that house cardholder data provides the opportunity for individuals to access devices or data and to remove systems or hardcopies',
    },
    {
        id: '10',
        title: 'Log and monitor all access to system components and cardholder data',
        description:
            'Logging mechanisms and the ability to track user activities are critical in preventing, detecting, or minimizing the impact of a data compromise',
    },
    {
        id: '11',
        title: 'Test security of systems and networks regularly',
        description:
            'Vulnerabilities are being discovered continually by malicious individuals and researchers, and being introduced by new software',
    },
    {
        id: '12',
        title: 'Support information security with organizational policies and programs',
        description:
            'A strong security policy sets the security tone for the whole entity and informs personnel what is expected of them',
    },
] as const

// Module configuration
export const MODULE_CONFIGS = [
    {
        id: 'framework-management',
        name: 'Framework Management',
        description: 'Manage compliance frameworks like PCI DSS 4.0.1',
        icon: 'HiOutlineClipboardList',
        path: '/dashboard/framework-management',
        enabled: true,
        permissions: ['read:frameworks', 'write:frameworks'],
    },

    {
        id: 'compliance-dashboard',
        name: 'Compliance Dashboard',
        description: 'High-level insights across modules',
        icon: 'HiOutlineChartBar',
        path: '/dashboard/compliance-dashboard',
        enabled: true,
        permissions: ['read:dashboard'],
    },
] as const
