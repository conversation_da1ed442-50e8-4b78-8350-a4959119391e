'use client'

import { useState } from 'react'
import Dialog from '@/components/ui/Dialog'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Select from '@/components/ui/Select'
import FormItem from '@/components/ui/Form/FormItem'
import FormContainer from '@/components/ui/Form/FormContainer'
import { CreateUserRequest } from '../types'
import { HiOutlineX } from 'react-icons/hi'

interface AddUserModalProps {
    isOpen: boolean
    onClose: () => void
    onSubmit: (userData: CreateUserRequest) => Promise<void>
    loading?: boolean
}

const AddUserModal = ({
    isOpen,
    onClose,
    onSubmit,
    loading = false,
}: AddUserModalProps) => {
    const [formData, setFormData] = useState<CreateUserRequest>({
        email: '',
        userName: '',
        firstName: '',
        lastName: '',
        role: 'user',
        sendInvite: true,
    })

    const [errors, setErrors] = useState<Record<string, string>>({})

    const validateForm = () => {
        const newErrors: Record<string, string> = {}

        if (!formData.email) {
            newErrors.email = 'Email is required'
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
            newErrors.email = 'Email is invalid'
        }

        if (!formData.userName) {
            newErrors.userName = 'Username is required'
        } else if (formData.userName.length < 3) {
            newErrors.userName = 'Username must be at least 3 characters'
        }

        if (!formData.firstName) {
            newErrors.firstName = 'First name is required'
        }

        if (!formData.lastName) {
            newErrors.lastName = 'Last name is required'
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!validateForm()) {
            return
        }

        try {
            await onSubmit(formData)
            handleClose()
        } catch (error) {
            console.error('Error creating user:', error)
        }
    }

    const handleClose = () => {
        setFormData({
            email: '',
            userName: '',
            firstName: '',
            lastName: '',
            role: 'user',
            sendInvite: true,
        })
        setErrors({})
        onClose()
    }

    const handleInputChange = (
        field: keyof CreateUserRequest,
        value: string | boolean,
    ) => {
        setFormData((prev) => ({ ...prev, [field]: value }))

        // Clear error when user starts typing
        if (errors[field]) {
            setErrors((prev) => ({ ...prev, [field]: '' }))
        }

        // Auto-generate username from email if username is empty
        if (
            field === 'email' &&
            !formData.userName &&
            typeof value === 'string'
        ) {
            const username = value.split('@')[0]
            setFormData((prev) => ({ ...prev, userName: username }))
        }
    }

    const roleOptions = [
        { value: 'user', label: 'User' },
        { value: 'admin', label: 'Administrator' },
    ]

    return (
        <Dialog isOpen={isOpen} onClose={handleClose} width={500}>
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold">Add New User</h3>
                <Button
                    variant="plain"
                    size="sm"
                    icon={<HiOutlineX />}
                    onClick={handleClose}
                />
            </div>

            <form onSubmit={handleSubmit}>
                <div className="p-6 space-y-4">
                    <FormContainer>
                        <FormItem
                            label="Email Address"
                            invalid={!!errors.email}
                            errorMessage={errors.email}
                        >
                            <Input
                                type="email"
                                placeholder="<EMAIL>"
                                value={formData.email}
                                onChange={(e) =>
                                    handleInputChange('email', e.target.value)
                                }
                                invalid={!!errors.email}
                            />
                        </FormItem>

                        <FormItem
                            label="Username"
                            invalid={!!errors.userName}
                            errorMessage={errors.userName}
                        >
                            <Input
                                placeholder="username"
                                value={formData.userName}
                                onChange={(e) =>
                                    handleInputChange(
                                        'userName',
                                        e.target.value,
                                    )
                                }
                                invalid={!!errors.userName}
                            />
                        </FormItem>

                        <div className="grid grid-cols-2 gap-4">
                            <FormItem
                                label="First Name"
                                invalid={!!errors.firstName}
                                errorMessage={errors.firstName}
                            >
                                <Input
                                    placeholder="John"
                                    value={formData.firstName}
                                    onChange={(e) =>
                                        handleInputChange(
                                            'firstName',
                                            e.target.value,
                                        )
                                    }
                                    invalid={!!errors.firstName}
                                />
                            </FormItem>

                            <FormItem
                                label="Last Name"
                                invalid={!!errors.lastName}
                                errorMessage={errors.lastName}
                            >
                                <Input
                                    placeholder="Doe"
                                    value={formData.lastName}
                                    onChange={(e) =>
                                        handleInputChange(
                                            'lastName',
                                            e.target.value,
                                        )
                                    }
                                    invalid={!!errors.lastName}
                                />
                            </FormItem>
                        </div>

                        <FormItem label="Role">
                            <Select
                                options={roleOptions}
                                value={roleOptions.find(
                                    (option) => option.value === formData.role,
                                )}
                                onChange={(option) =>
                                    handleInputChange(
                                        'role',
                                        option?.value || 'user',
                                    )
                                }
                            />
                        </FormItem>

                        <FormItem>
                            <label className="flex items-center gap-2">
                                <input
                                    type="checkbox"
                                    checked={formData.sendInvite}
                                    onChange={(e) =>
                                        handleInputChange(
                                            'sendInvite',
                                            e.target.checked,
                                        )
                                    }
                                    className="rounded border-gray-300"
                                />
                                <span className="text-sm">
                                    Send invitation email
                                </span>
                            </label>
                        </FormItem>
                    </FormContainer>
                </div>

                <div className="flex justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
                    <Button
                        type="button"
                        variant="plain"
                        onClick={handleClose}
                        disabled={loading}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="submit"
                        variant="solid"
                        loading={loading}
                        className="bg-emerald-500 hover:bg-emerald-600"
                    >
                        Add User
                    </Button>
                </div>
            </form>
        </Dialog>
    )
}

export default AddUserModal
