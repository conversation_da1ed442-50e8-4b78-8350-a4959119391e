'use client'

import { RiskList } from '../components'
import { useMockRisks, useRiskExport } from '../hooks'
import { Risk } from '@/shared/types/compliance'

const RiskManagementPage = () => {
    const { data: risks, loading, error } = useMockRisks()
    const { exportRisksToCSV } = useRiskExport()

    const handleViewRisk = (risk: Risk) => {
        console.log('View risk:', risk)
        // TODO: Navigate to risk detail page or open modal
    }

    const handleEditRisk = (risk: Risk) => {
        console.log('Edit risk:', risk)
        // TODO: Open edit modal or navigate to edit page
    }

    const handleViewControls = (risk: Risk) => {
        console.log('View controls for risk:', risk)
        // TODO: Navigate to control hub with risk filter
    }

    const handleUpdateStatus = async (risk: Risk) => {
        console.log('Update status for risk:', risk)
        // TODO: Open status update modal
    }

    const handleAddRisk = () => {
        console.log('Add new risk')
        // TODO: Open add risk modal or navigate to add page
    }

    const handleExportCSV = () => {
        exportRisksToCSV(risks)
    }

    if (error) {
        return (
            <div className="p-6">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h3 className="text-red-800 font-medium">
                        Error loading risks
                    </h3>
                    <p className="text-red-600 text-sm mt-1">{error}</p>
                </div>
            </div>
        )
    }

    return (
        <div className="p-6">
            <RiskList
                risks={risks}
                loading={loading}
                onView={handleViewRisk}
                onEdit={handleEditRisk}
                onViewControls={handleViewControls}
                onUpdateStatus={handleUpdateStatus}
                onAddRisk={handleAddRisk}
                onExportCSV={handleExportCSV}
            />
        </div>
    )
}

export default RiskManagementPage
