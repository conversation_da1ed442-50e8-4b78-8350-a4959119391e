'use client'

import { useState, useCallback } from 'react'
import {
    useFrameworks as useFrameworksBase,
    useSupabaseMutation,
} from '@/shared/hooks'
import {
    Framework,
    FilterOptions,
    PaginationOptions,
} from '@/shared/types/compliance'

export const useFrameworks = (
    filters?: FilterOptions,
    pagination?: PaginationOptions,
) => {
    return useFrameworksBase(filters, pagination)
}

export const useFrameworkMutations = () => {
    const { create, update, remove, loading, error } =
        useSupabaseMutation<Framework>('frameworks')

    const createFramework = useCallback(
        async (frameworkData: Partial<Framework>) => {
            const newFramework = {
                ...frameworkData,
                id: crypto.randomUUID(),
                progress: 0,
                notifications: 0,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                organizationId: 'default-org', // This should come from auth context
            }

            return await create(newFramework)
        },
        [create],
    )

    const updateFramework = useCallback(
        async (id: string, updates: Partial<Framework>) => {
            const updatedData = {
                ...updates,
                updatedAt: new Date().toISOString(),
            }

            return await update(id, updatedData)
        },
        [update],
    )

    const deleteFramework = useCallback(
        async (id: string) => {
            return await remove(id)
        },
        [remove],
    )

    return {
        createFramework,
        updateFramework,
        deleteFramework,
        loading,
        error,
    }
}

// Mock data for development - remove when Supabase is set up
export const useMockFrameworks = () => {
    const [frameworks] = useState<Framework[]>([
        {
            id: 'pci-dss-401',
            name: 'PCI DSS 4.0.1',
            version: '4.0.1',
            category: 'Cybersecurity',
            description:
                'Payment Card Industry Data Security Standard for organizations that handle credit cards.',
            region: 'Global',
            status: 'Active',
            controlCount: 264,
            progress: 62,
            lastUpdated: '2023-11-15',
            notifications: 3,
            createdAt: '2023-01-15T00:00:00Z',
            updatedAt: '2023-11-15T00:00:00Z',
            createdBy: 'admin',
            organizationId: 'default-org',
        },
        {
            id: 'iso-27001',
            name: 'ISO/IEC 27001',
            version: '2022',
            category: 'Cybersecurity',
            description:
                'International standard for information security management systems.',
            region: 'Global',
            status: 'Active',
            controlCount: 114,
            progress: 78,
            lastUpdated: '2023-10-22',
            notifications: 0,
            createdAt: '2023-02-01T00:00:00Z',
            updatedAt: '2023-10-22T00:00:00Z',
            createdBy: 'admin',
            organizationId: 'default-org',
        },
        {
            id: 'sox',
            name: 'Sarbanes-Oxley (SOX)',
            category: 'Financial',
            description:
                'Regulations for financial reporting and corporate governance.',
            region: 'US',
            status: 'In Progress',
            controlCount: 87,
            progress: 45,
            lastUpdated: '2023-12-05',
            notifications: 5,
            createdAt: '2023-03-01T00:00:00Z',
            updatedAt: '2023-12-05T00:00:00Z',
            createdBy: 'admin',
            organizationId: 'default-org',
        },
        {
            id: 'gdpr',
            name: 'GDPR',
            category: 'Privacy',
            description:
                'General Data Protection Regulation for data privacy in the EU.',
            region: 'EU',
            status: 'Active',
            controlCount: 99,
            progress: 91,
            lastUpdated: '2023-09-18',
            notifications: 0,
            createdAt: '2023-01-20T00:00:00Z',
            updatedAt: '2023-09-18T00:00:00Z',
            createdBy: 'admin',
            organizationId: 'default-org',
        },
        {
            id: 'hipaa',
            name: 'HIPAA',
            category: 'Healthcare',
            description:
                'Health Insurance Portability and Accountability Act for medical information privacy.',
            region: 'US',
            status: 'Not Started',
            controlCount: 75,
            progress: 30,
            lastUpdated: '2023-12-10',
            notifications: 2,
            createdAt: '2023-04-01T00:00:00Z',
            updatedAt: '2023-12-10T00:00:00Z',
            createdBy: 'admin',
            organizationId: 'default-org',
        },
    ])

    return {
        data: frameworks,
        loading: false,
        error: null,
        total: frameworks.length,
        refetch: () => {},
    }
}
