#!/usr/bin/env node

/**
 * Script to setup branch protection rules for CheckGap repository
 * Run with: node scripts/setup-branch-protection.js
 */

const { Octokit } = require('@octokit/rest')

// Configuration
const REPO_OWNER = 'Hu4nd3r'
const REPO_NAME = 'checkgap2'

// Required environment variables
const GITHUB_TOKEN = process.env.GITHUB_TOKEN

if (!GITHUB_TOKEN) {
    console.error('❌ GITHUB_TOKEN environment variable is required')
    console.log(
        'Create a personal access token at: https://github.com/settings/tokens',
    )
    console.log('Required scopes: repo, admin:repo_hook')
    process.exit(1)
}

const octokit = new Octokit({
    auth: GITHUB_TOKEN,
})

async function setupBranchProtection() {
    console.log('🔧 Setting up branch protection rules...\n')

    try {
        // Main branch protection
        console.log('🛡️ Setting up main branch protection...')
        await octokit.rest.repos.updateBranchProtection({
            owner: REPO_OWNER,
            repo: REPO_NAME,
            branch: 'main',
            required_status_checks: {
                strict: true,
                contexts: [
                    'Code Quality',
                    'Automated Tests',
                    'Build Verification',
                    'Security Checks',
                ],
            },
            enforce_admins: true,
            required_pull_request_reviews: {
                required_approving_review_count: 1,
                dismiss_stale_reviews: true,
                require_code_owner_reviews: false,
                require_last_push_approval: true,
            },
            restrictions: null,
            required_linear_history: true,
            allow_force_pushes: false,
            allow_deletions: false,
        })
        console.log('✅ Main branch protection configured')

        // Develop branch protection
        console.log('🛡️ Setting up develop branch protection...')
        await octokit.rest.repos.updateBranchProtection({
            owner: REPO_OWNER,
            repo: REPO_NAME,
            branch: 'develop',
            required_status_checks: {
                strict: true,
                contexts: [
                    'Code Quality',
                    'Automated Tests',
                    'Build Verification',
                    'Security Checks',
                ],
            },
            enforce_admins: false,
            required_pull_request_reviews: {
                required_approving_review_count: 1,
                dismiss_stale_reviews: true,
                require_code_owner_reviews: false,
                require_last_push_approval: false,
            },
            restrictions: null,
            required_linear_history: false,
            allow_force_pushes: true,
            allow_deletions: false,
        })
        console.log('✅ Develop branch protection configured')

        console.log('\n🎉 Branch protection setup completed successfully!')
        console.log('\n📋 Summary:')
        console.log('• Main branch: Strict protection with required reviews')
        console.log(
            '• Develop branch: Standard protection with required reviews',
        )
        console.log(
            '• Required status checks: Code quality, tests, build, security',
        )
        console.log('• Linear history required for main branch')
    } catch (error) {
        console.error('❌ Error setting up branch protection:', error.message)

        if (error.status === 404) {
            console.log('\n💡 Possible solutions:')
            console.log(
                '• Ensure the repository exists and you have admin access',
            )
            console.log('• Check that the branch names are correct')
            console.log(
                '• Verify your GitHub token has the required permissions',
            )
        }

        if (error.status === 403) {
            console.log('\n💡 Permission issue:')
            console.log('• Ensure your GitHub token has admin:repo_hook scope')
            console.log('• You need admin access to the repository')
        }

        process.exit(1)
    }
}

async function checkRepository() {
    try {
        console.log('🔍 Checking repository access...')
        const { data: repo } = await octokit.rest.repos.get({
            owner: REPO_OWNER,
            repo: REPO_NAME,
        })

        console.log(`✅ Repository found: ${repo.full_name}`)
        console.log(`📊 Repository info:`)
        console.log(`   • Private: ${repo.private}`)
        console.log(`   • Default branch: ${repo.default_branch}`)
        console.log(
            `   • Admin permissions: ${repo.permissions?.admin || 'Unknown'}\n`,
        )

        return true
    } catch (error) {
        console.error('❌ Cannot access repository:', error.message)
        return false
    }
}

async function listBranches() {
    try {
        console.log('🌿 Checking available branches...')
        const { data: branches } = await octokit.rest.repos.listBranches({
            owner: REPO_OWNER,
            repo: REPO_NAME,
        })

        console.log('📋 Available branches:')
        branches.forEach((branch) => {
            console.log(
                `   • ${branch.name}${branch.protected ? ' (protected)' : ''}`,
            )
        })
        console.log('')

        return branches
    } catch (error) {
        console.error('❌ Cannot list branches:', error.message)
        return []
    }
}

// Main execution
async function main() {
    console.log('🚀 CheckGap Branch Protection Setup\n')

    // Check repository access
    const hasAccess = await checkRepository()
    if (!hasAccess) {
        process.exit(1)
    }

    // List branches
    const branches = await listBranches()
    const branchNames = branches.map((b) => b.name)

    // Check if required branches exist
    if (!branchNames.includes('main')) {
        console.error('❌ Main branch not found')
        process.exit(1)
    }

    if (!branchNames.includes('develop')) {
        console.error('❌ Develop branch not found')
        process.exit(1)
    }

    // Setup protection
    await setupBranchProtection()
}

// Run the script
main().catch((error) => {
    console.error('❌ Unexpected error:', error)
    process.exit(1)
})
