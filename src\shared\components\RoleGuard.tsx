'use client'

import { ReactNode } from 'react'
import { useAuth } from '@/shared/hooks/useAuth'

interface RoleGuardProps {
    children: ReactNode
    allowedRoles?: string[]
    requiredPermissions?: string[]
    fallback?: ReactNode
    requireAll?: boolean // If true, user must have ALL permissions/roles. If false, user needs ANY
}

const RoleGuard = ({
    children,
    allowedRoles = [],
    requiredPermissions = [],
    fallback = null,
    requireAll = false,
}: RoleGuardProps) => {
    const { hasAnyRole, hasPermission, isAuthenticated } = useAuth()

    // If not authenticated, don't render anything
    if (!isAuthenticated) {
        return <>{fallback}</>
    }

    // Check role-based access
    if (allowedRoles.length > 0) {
        const hasRequiredRole = requireAll
            ? allowedRoles.every((role) => hasAnyRole([role]))
            : hasAnyRole(allowedRoles)

        if (!hasRequiredRole) {
            return <>{fallback}</>
        }
    }

    // Check permission-based access
    if (requiredPermissions.length > 0) {
        const hasRequiredPermission = requireAll
            ? requiredPermissions.every((permission) =>
                  hasPermission(permission),
              )
            : requiredPermissions.some((permission) =>
                  hasPermission(permission),
              )

        if (!hasRequiredPermission) {
            return <>{fallback}</>
        }
    }

    return <>{children}</>
}

export default RoleGuard
