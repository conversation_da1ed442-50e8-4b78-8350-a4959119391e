'use client'

import { useState, useId } from 'react'
import Input from '@/components/ui/Input'
import Select from '@/components/ui/Select'
import Button from '@/components/ui/Button'
import { HiOutlineSearch, HiOutlineFilter, HiX } from 'react-icons/hi'
import { FilterOptions } from '../types/compliance'

interface FilterOption {
    value: string
    label: string
}

interface FilterBarProps {
    searchPlaceholder?: string
    filters?: {
        status?: FilterOption[]
        category?: FilterOption[]
        owner?: FilterOption[]
        assignee?: FilterOption[]
        priority?: FilterOption[]
        type?: FilterOption[]
    }
    onFilterChange: (filters: FilterOptions) => void
    className?: string
}

const FilterBar = ({
    searchPlaceholder = 'Search...',
    filters = {},
    onFilterChange,
    className = '',
}: FilterBarProps) => {
    const [searchTerm, setSearchTerm] = useState('')
    const [selectedFilters, setSelectedFilters] = useState<FilterOptions>({})
    const [showFilters, setShowFilters] = useState(false)

    // Generate stable IDs for select components
    const statusSelectId = useId()
    const categorySelectId = useId()
    const ownerSelectId = useId()
    const assigneeSelectId = useId()
    const prioritySelectId = useId()
    const typeSelectId = useId()

    const handleSearchChange = (value: string) => {
        setSearchTerm(value)
        const newFilters = { ...selectedFilters, search: value || undefined }
        setSelectedFilters(newFilters)
        onFilterChange(newFilters)
    }

    const handleFilterChange = (
        filterType: keyof FilterOptions,
        value: FilterOption | null,
    ) => {
        const newFilters = {
            ...selectedFilters,
            [filterType]: value?.value ? [value.value] : undefined,
        }
        setSelectedFilters(newFilters)
        onFilterChange(newFilters)
    }

    const clearFilters = () => {
        setSearchTerm('')
        setSelectedFilters({})
        onFilterChange({})
    }

    const hasActiveFilters = Object.values(selectedFilters).some(
        (filter) =>
            filter !== undefined &&
            filter !== '' &&
            (!Array.isArray(filter) || filter.length > 0),
    )

    const getSelectedValue = (
        filterType: keyof FilterOptions,
        options: FilterOption[],
    ) => {
        const selectedValue = selectedFilters[filterType]
        if (Array.isArray(selectedValue) && selectedValue.length > 0) {
            const option = options.find((opt) => opt.value === selectedValue[0])
            return option ? { value: option.value, label: option.label } : null
        }
        return null
    }

    return (
        <div className={`space-y-4 ${className}`}>
            {/* Search and Filter Toggle */}
            <div className="flex flex-col sm:flex-row gap-3">
                <div className="flex-1">
                    <Input
                        prefix={<HiOutlineSearch className="text-lg" />}
                        placeholder={searchPlaceholder}
                        value={searchTerm}
                        onChange={(e) => handleSearchChange(e.target.value)}
                    />
                </div>
                <div className="flex gap-2">
                    <Button
                        variant={showFilters ? 'solid' : 'default'}
                        icon={<HiOutlineFilter />}
                        onClick={() => setShowFilters(!showFilters)}
                    >
                        Filters
                    </Button>
                    {hasActiveFilters && (
                        <Button
                            variant="plain"
                            icon={<HiX />}
                            onClick={clearFilters}
                        >
                            Clear
                        </Button>
                    )}
                </div>
            </div>

            {/* Filter Options */}
            {showFilters && (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                    {filters.status && (
                        <Select
                            placeholder="Status"
                            options={filters.status}
                            value={getSelectedValue('status', filters.status)}
                            onChange={(option) =>
                                handleFilterChange('status', option)
                            }
                            isClearable
                            instanceId={statusSelectId}
                        />
                    )}

                    {filters.category && (
                        <Select
                            placeholder="Category"
                            options={filters.category}
                            value={getSelectedValue(
                                'category',
                                filters.category,
                            )}
                            onChange={(option) =>
                                handleFilterChange('category', option)
                            }
                            isClearable
                            instanceId={categorySelectId}
                        />
                    )}

                    {filters.owner && (
                        <Select
                            placeholder="Owner"
                            options={filters.owner}
                            value={getSelectedValue('owner', filters.owner)}
                            onChange={(option) =>
                                handleFilterChange('owner', option)
                            }
                            isClearable
                            instanceId={ownerSelectId}
                        />
                    )}

                    {filters.assignee && (
                        <Select
                            placeholder="Assignee"
                            options={filters.assignee}
                            value={getSelectedValue(
                                'assignee',
                                filters.assignee,
                            )}
                            onChange={(option) =>
                                handleFilterChange('assignee', option)
                            }
                            isClearable
                            instanceId={assigneeSelectId}
                        />
                    )}

                    {filters.priority && (
                        <Select
                            placeholder="Priority"
                            options={filters.priority}
                            value={getSelectedValue(
                                'priority',
                                filters.priority,
                            )}
                            onChange={(option) =>
                                handleFilterChange('priority', option)
                            }
                            isClearable
                            instanceId={prioritySelectId}
                        />
                    )}

                    {filters.type && (
                        <Select
                            placeholder="Type"
                            options={filters.type}
                            value={getSelectedValue('type', filters.type)}
                            onChange={(option) =>
                                handleFilterChange('type', option)
                            }
                            isClearable
                            instanceId={typeSelectId}
                        />
                    )}
                </div>
            )}

            {/* Active Filters Display */}
            {hasActiveFilters && (
                <div className="flex flex-wrap gap-2">
                    {Object.entries(selectedFilters).map(([key, value]) => {
                        if (
                            !value ||
                            (Array.isArray(value) && value.length === 0)
                        )
                            return null

                        const displayValue = Array.isArray(value)
                            ? value.join(', ')
                            : value

                        return (
                            <span
                                key={key}
                                className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-md dark:bg-blue-900/30 dark:text-blue-400"
                            >
                                <span className="capitalize">{key}:</span>
                                <span>{displayValue}</span>
                                <button
                                    onClick={() =>
                                        handleFilterChange(
                                            key as keyof FilterOptions,
                                            null,
                                        )
                                    }
                                    className="hover:text-blue-900 dark:hover:text-blue-300"
                                >
                                    <HiX className="w-3 h-3" />
                                </button>
                            </span>
                        )
                    })}
                </div>
            )}
        </div>
    )
}

export default FilterBar
