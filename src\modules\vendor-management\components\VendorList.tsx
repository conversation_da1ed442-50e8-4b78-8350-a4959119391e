import { useState, useMemo } from 'react'
import { FilterBar } from '@/shared/components'
import { FilterOptions } from '@/shared/types/compliance'
import {
    VENDOR_CATEGORIES,
    VENDOR_STATUSES,
    RISK_LEVELS,
} from '@/shared/constants/compliance'
import VendorCard from './VendorCard'
import { Vendor } from '@/shared/types/compliance'
import Button from '@/components/ui/Button'
import Tabs from '@/components/ui/Tabs'
import {
    HiPlus,
    HiOutlineDownload,
    HiOutlineViewGrid,
    HiOutlineViewList,
} from 'react-icons/hi'

interface VendorListProps {
    vendors: Vendor[]
    loading?: boolean
    onView?: (vendor: Vendor) => void
    onEdit?: (vendor: Vendor) => void
    onViewDocuments?: (vendor: Vendor) => void
    onViewControls?: (vendor: Vendor) => void
    onAddVendor?: () => void
    onExportCSV?: () => void
}

const VendorList = ({
    vendors,
    loading = false,
    onView,
    onEdit,
    onViewDocuments,
    onViewControls,
    onAddVendor,
    onExportCSV,
}: VendorListProps) => {
    const [filters, setFilters] = useState<FilterOptions>({})
    const [activeTab, setActiveTab] = useState('all')
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

    // Filter options for the FilterBar
    const filterOptions = {
        status: VENDOR_STATUSES.map((status) => ({
            value: status,
            label: status,
        })),
        category: VENDOR_CATEGORIES.map((category) => ({
            value: category,
            label: category,
        })),
        priority: RISK_LEVELS.map((level) => ({ value: level, label: level })),
    }

    // Filter vendors based on current filters and active tab
    const filteredVendors = useMemo(() => {
        let filtered = vendors

        // Apply tab filter first
        switch (activeTab) {
            case 'high-risk':
                filtered = filtered.filter(
                    (vendor) =>
                        vendor.riskLevel === 'High' ||
                        vendor.riskLevel === 'Critical',
                )
                break
            case 'expiring-soon':
                filtered = filtered.filter((vendor) => {
                    if (!vendor.contractEndDate) return false
                    const endDate = new Date(vendor.contractEndDate)
                    const today = new Date()
                    const daysUntilExpiry = Math.ceil(
                        (endDate.getTime() - today.getTime()) /
                            (1000 * 60 * 60 * 24),
                    )
                    return daysUntilExpiry <= 90 && daysUntilExpiry > 0
                })
                break
            case 'assessment-due':
                filtered = filtered.filter(
                    (vendor) =>
                        vendor.nextAssessmentDate &&
                        new Date(vendor.nextAssessmentDate) <= new Date(),
                )
                break
            case 'inactive':
                filtered = filtered.filter(
                    (vendor) =>
                        vendor.status === 'Inactive' ||
                        vendor.status === 'Terminated',
                )
                break
            default:
                // 'all' - no additional filtering
                break
        }

        // Apply search and other filters
        return filtered.filter((vendor) => {
            // Search filter
            if (filters.search) {
                const searchTerm = filters.search.toLowerCase()
                const matchesSearch =
                    vendor.name.toLowerCase().includes(searchTerm) ||
                    (vendor.description &&
                        vendor.description
                            .toLowerCase()
                            .includes(searchTerm)) ||
                    vendor.category.toLowerCase().includes(searchTerm) ||
                    vendor.tags.some((tag) =>
                        tag.toLowerCase().includes(searchTerm),
                    )

                if (!matchesSearch) return false
            }

            // Status filter
            if (filters.status && filters.status.length > 0) {
                if (!filters.status.includes(vendor.status)) return false
            }

            // Category filter
            if (filters.category && filters.category.length > 0) {
                if (!filters.category.includes(vendor.category)) return false
            }

            // Risk level filter
            if (filters.priority && filters.priority.length > 0) {
                if (!filters.priority.includes(vendor.riskLevel)) return false
            }

            return true
        })
    }, [vendors, filters, activeTab])

    // Calculate stats
    const stats = useMemo(() => {
        const highRiskVendors = vendors.filter(
            (vendor) =>
                vendor.riskLevel === 'High' || vendor.riskLevel === 'Critical',
        )
        const expiringSoon = vendors.filter((vendor) => {
            if (!vendor.contractEndDate) return false
            const endDate = new Date(vendor.contractEndDate)
            const today = new Date()
            const daysUntilExpiry = Math.ceil(
                (endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24),
            )
            return daysUntilExpiry <= 90 && daysUntilExpiry > 0
        })
        const assessmentDue = vendors.filter(
            (vendor) =>
                vendor.nextAssessmentDate &&
                new Date(vendor.nextAssessmentDate) <= new Date(),
        )
        const inactive = vendors.filter(
            (vendor) =>
                vendor.status === 'Inactive' || vendor.status === 'Terminated',
        )

        return {
            total: vendors.length,
            highRisk: highRiskVendors.length,
            expiringSoon: expiringSoon.length,
            assessmentDue: assessmentDue.length,
            inactive: inactive.length,
        }
    }, [vendors])

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="animate-pulse">
                    <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4"></div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {Array.from({ length: 6 }).map((_, i) => (
                            <div
                                key={i}
                                className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg"
                            ></div>
                        ))}
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 className="text-2xl font-bold">Vendor Management</h1>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">
                        Manage third-party vendors and service providers
                    </p>
                </div>
                <div className="flex gap-2">
                    <Button
                        variant="default"
                        icon={<HiOutlineDownload />}
                        onClick={onExportCSV}
                    >
                        Export CSV
                    </Button>
                    <Button
                        variant="solid"
                        icon={<HiPlus />}
                        onClick={onAddVendor}
                        className="bg-emerald-500 hover:bg-emerald-600"
                    >
                        Add Vendor
                    </Button>
                </div>
            </div>

            {/* Tabs */}
            <Tabs
                value={activeTab}
                onChange={(val) => setActiveTab(val as string)}
            >
                <Tabs.TabList>
                    <Tabs.TabNav value="all">
                        All Vendors ({stats.total})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="high-risk">
                        High Risk ({stats.highRisk})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="expiring-soon">
                        Expiring Soon ({stats.expiringSoon})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="assessment-due">
                        Assessment Due ({stats.assessmentDue})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="inactive">
                        Inactive ({stats.inactive})
                    </Tabs.TabNav>
                </Tabs.TabList>
            </Tabs>

            {/* Filters and View Toggle */}
            <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                    <FilterBar
                        searchPlaceholder="Search vendors..."
                        filters={filterOptions}
                        onFilterChange={setFilters}
                    />
                </div>
                <div className="flex gap-2">
                    <Button
                        variant={viewMode === 'grid' ? 'solid' : 'default'}
                        icon={<HiOutlineViewGrid />}
                        onClick={() => setViewMode('grid')}
                        size="sm"
                    />
                    <Button
                        variant={viewMode === 'list' ? 'solid' : 'default'}
                        icon={<HiOutlineViewList />}
                        onClick={() => setViewMode('list')}
                        size="sm"
                    />
                </div>
            </div>

            {/* Vendor Display */}
            {filteredVendors.length > 0 ? (
                <div
                    className={
                        viewMode === 'grid'
                            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                            : 'space-y-3'
                    }
                >
                    {filteredVendors.map((vendor) => (
                        <VendorCard
                            key={vendor.id}
                            vendor={vendor}
                            compact={viewMode === 'list'}
                            onView={onView}
                            onEdit={onEdit}
                            onViewDocuments={onViewDocuments}
                            onViewControls={onViewControls}
                        />
                    ))}
                </div>
            ) : (
                <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                        <svg
                            className="w-16 h-16 mx-auto"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={1}
                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                            />
                        </svg>
                    </div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                        No vendors found
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                        {Object.keys(filters).length > 0 || activeTab !== 'all'
                            ? 'Try adjusting your filters or tab selection to find what you need.'
                            : 'Get started by adding your first vendor or service provider.'}
                    </p>
                    <Button
                        variant="solid"
                        icon={<HiPlus />}
                        onClick={onAddVendor}
                        className="bg-emerald-500 hover:bg-emerald-600"
                    >
                        Add Vendor
                    </Button>
                </div>
            )}
        </div>
    )
}

export default VendorList
