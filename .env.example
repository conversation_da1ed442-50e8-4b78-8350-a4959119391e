# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# NextAuth Configuration (if you want to keep both systems)
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_secret_key

# OAuth Providers (configure these in Supabase dashboard)
# Google OAuth
GOOGLE_AUTH_CLIENT_ID=your_google_client_id
GOOGLE_AUTH_CLIENT_SECRET=your_google_client_secret

# GitHub OAuth
GITHUB_AUTH_CLIENT_ID=your_github_client_id
GITHUB_AUTH_CLIENT_SECRET=your_github_client_secret

# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000/api
