'use client'

import { useState, useEffect, useMemo } from 'react'
import { PCIDSSRequirement, PCIDSSMetrics, PCIDSSComplianceStatus } from '../types'
import { PCI_DSS_REQUIREMENTS } from '../data/requirements'

export interface UsePCIDSSReturn {
    requirements: PCIDSSRequirement[]
    metrics: PCIDSSMetrics
    loading: boolean
    error: string | null
    updateRequirementStatus: (id: string, status: PCIDSSComplianceStatus) => void
    updateRequirement: (id: string, updates: Partial<PCIDSSRequirement>) => void
    getRequirementsByCategory: (category: string) => PCIDSSRequirement[]
    getRequirementsByStatus: (status: PCIDSSComplianceStatus) => PCIDSSRequirement[]
    refreshData: () => void
}

export const usePCIDSS = (): UsePCIDSSReturn => {
    const [requirements, setRequirements] = useState<PCIDSSRequirement[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    // Initialize with default PCI DSS requirements
    useEffect(() => {
        const initializeRequirements = async () => {
            try {
                setLoading(true)
                // In a real implementation, this would fetch from an API
                // For now, we'll use the static data
                setRequirements(PCI_DSS_REQUIREMENTS)
                setError(null)
            } catch (err) {
                setError(err instanceof Error ? err.message : 'Failed to load PCI DSS requirements')
            } finally {
                setLoading(false)
            }
        }

        initializeRequirements()
    }, [])

    // Calculate metrics based on current requirements
    const metrics = useMemo((): PCIDSSMetrics => {
        const totalRequirements = requirements.length
        const compliantRequirements = requirements.filter(req => req.status === 'Compliant').length
        const nonCompliantRequirements = requirements.filter(req => req.status === 'Non-Compliant').length
        const inProgressRequirements = requirements.filter(req => req.status === 'In Progress').length
        const notStartedRequirements = requirements.filter(req => req.status === 'Not Started').length
        
        const compliancePercentage = totalRequirements > 0 
            ? Math.round((compliantRequirements / totalRequirements) * 100)
            : 0

        // Mock findings data - in real implementation, this would come from assessments
        const criticalFindings = requirements.filter(req => req.riskLevel === 'Critical' && req.status !== 'Compliant').length
        const highFindings = requirements.filter(req => req.riskLevel === 'High' && req.status !== 'Compliant').length
        const mediumFindings = requirements.filter(req => req.riskLevel === 'Medium' && req.status !== 'Compliant').length
        const lowFindings = requirements.filter(req => req.riskLevel === 'Low' && req.status !== 'Compliant').length

        // Mock overdue tasks and upcoming deadlines
        const now = new Date()
        const overdueTasks = requirements.filter(req => {
            if (!req.dueDate || req.status === 'Compliant') return false
            return new Date(req.dueDate) < now
        }).length

        const upcomingDeadlines = requirements.filter(req => {
            if (!req.dueDate || req.status === 'Compliant') return false
            const dueDate = new Date(req.dueDate)
            const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
            return dueDate >= now && dueDate <= thirtyDaysFromNow
        }).length

        return {
            totalRequirements,
            compliantRequirements,
            nonCompliantRequirements,
            inProgressRequirements,
            notStartedRequirements,
            compliancePercentage,
            criticalFindings,
            highFindings,
            mediumFindings,
            lowFindings,
            overdueTasks,
            upcomingDeadlines,
        }
    }, [requirements])

    // Update requirement status
    const updateRequirementStatus = (id: string, status: PCIDSSComplianceStatus) => {
        setRequirements(prev => prev.map(req => 
            req.id === id 
                ? { ...req, status, updatedAt: new Date().toISOString() }
                : req
        ))
    }

    // Update requirement with partial data
    const updateRequirement = (id: string, updates: Partial<PCIDSSRequirement>) => {
        setRequirements(prev => prev.map(req => 
            req.id === id 
                ? { ...req, ...updates, updatedAt: new Date().toISOString() }
                : req
        ))
    }

    // Get requirements by category
    const getRequirementsByCategoryFn = (category: string) => {
        return requirements.filter(req => req.category === category)
    }

    // Get requirements by status
    const getRequirementsByStatusFn = (status: PCIDSSComplianceStatus) => {
        return requirements.filter(req => req.status === status)
    }

    // Refresh data (for future API integration)
    const refreshData = () => {
        setRequirements(PCI_DSS_REQUIREMENTS)
    }

    return {
        requirements,
        metrics,
        loading,
        error,
        updateRequirementStatus,
        updateRequirement,
        getRequirementsByCategory: getRequirementsByCategoryFn,
        getRequirementsByStatus: getRequirementsByStatusFn,
        refreshData,
    }
}

// Hook for getting PCI DSS compliance summary
export const usePCIDSSCompliance = () => {
    const { requirements, metrics, loading } = usePCIDSS()

    const complianceSummary = useMemo(() => {
        const categories = [
            'Build and Maintain a Secure Network and Systems',
            'Protect Cardholder Data',
            'Maintain a Vulnerability Management Program',
            'Implement Strong Access Control Measures',
            'Regularly Monitor and Test Networks',
            'Maintain an Information Security Policy',
        ]

        return categories.map(category => {
            const categoryRequirements = requirements.filter(req => req.category === category)
            const compliant = categoryRequirements.filter(req => req.status === 'Compliant').length
            const total = categoryRequirements.length
            const percentage = total > 0 ? Math.round((compliant / total) * 100) : 0

            return {
                category,
                total,
                compliant,
                percentage,
                requirements: categoryRequirements,
            }
        })
    }, [requirements])

    return {
        complianceSummary,
        overallMetrics: metrics,
        loading,
    }
}
