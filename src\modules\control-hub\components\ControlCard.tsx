'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { StatusBadge } from '@/shared/components'
import { ControlDetail } from '../types'
import {
    HiOutlineClipboardCheck,
    HiOutlinePhotograph,
    HiOutlineCog,
    HiOutlineEye,
} from 'react-icons/hi'

interface ControlCardProps {
    control: ControlDetail
    onView?: (control: ControlDetail) => void
    onEdit?: (control: ControlDetail) => void
    onViewEvidence?: (control: ControlDetail) => void
    onViewTasks?: (control: ControlDetail) => void
    compact?: boolean
}

const ControlCard = ({
    control,
    onView,
    onEdit,
    onViewEvidence,
    onViewTasks,
    compact = false,
}: ControlCardProps) => {
    const [isHovered, setIsHovered] = useState(false)

    if (compact) {
        return (
            <div
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-colors border-gray-200 dark:border-gray-700"
                onClick={() => onView?.(control)}
            >
                <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                        <HiOutlineClipboardCheck className="w-4 h-4 text-gray-500" />
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            {control.controlNumber}
                        </span>
                    </div>

                    <div className="flex-1 min-w-0">
                        <h4 className="font-medium truncate">
                            {control.title}
                        </h4>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                            <span>{control.framework?.name}</span>
                            <span>•</span>
                            <span>{control.category}</span>
                        </div>
                    </div>
                </div>

                <div className="flex items-center gap-2">
                    <StatusBadge status={control.status} size="sm" />
                    <StatusBadge status={control.riskLevel} size="sm" />
                    <StatusBadge status={control.evidenceReadiness} size="sm" />
                </div>
            </div>
        )
    }

    return (
        <Card
            clickable
            className="h-full transition-all duration-200 hover:shadow-lg"
            bodyClass="h-full flex flex-col p-4"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={() => onView?.(control)}
        >
            {/* Header */}
            <div className="flex justify-between items-start mb-3">
                <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                        {control.controlNumber}
                    </span>
                    <StatusBadge status={control.status} size="sm" />
                </div>
                <StatusBadge status={control.riskLevel} size="sm" />
            </div>

            {/* Control Info */}
            <div className="flex-1">
                <h4 className="font-semibold mb-2 line-clamp-2">
                    {control.title}
                </h4>

                <div className="text-xs text-gray-500 mb-2">
                    {control.framework?.name} • {control.category}
                    {control.subcategory && ` • ${control.subcategory}`}
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-3">
                    {control.description}
                </p>

                {/* Requirements */}
                {control.requirements.length > 0 && (
                    <div className="mb-3">
                        <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Requirements:
                        </div>
                        <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                            {control.requirements
                                .slice(0, 2)
                                .map((req, index) => (
                                    <li
                                        key={index}
                                        className="flex items-start gap-1"
                                    >
                                        <span className="w-1 h-1 bg-gray-400 rounded-full mt-1.5 flex-shrink-0"></span>
                                        <span className="line-clamp-1">
                                            {req}
                                        </span>
                                    </li>
                                ))}
                            {control.requirements.length > 2 && (
                                <li className="text-gray-500">
                                    +{control.requirements.length - 2} more
                                </li>
                            )}
                        </ul>
                    </div>
                )}
            </div>

            {/* Status Indicators */}
            <div className="flex justify-between items-center mb-3 text-xs">
                <div className="flex items-center gap-3">
                    <div className="flex items-center gap-1">
                        <HiOutlineClipboardCheck className="w-3 h-3 text-gray-500" />
                        <span>{control.tasks.length} tasks</span>
                    </div>
                    <div className="flex items-center gap-1">
                        <HiOutlinePhotograph className="w-3 h-3 text-gray-500" />
                        <span>{control.evidence.length} evidence</span>
                    </div>
                </div>
                <StatusBadge status={control.evidenceReadiness} size="sm" />
            </div>

            {/* Footer */}
            <div className="flex justify-between items-center pt-3 border-t border-gray-200 dark:border-gray-700">
                <div className="text-xs text-gray-500">
                    {control.owner && `Owner: ${control.owner}`}
                </div>

                {/* Action Buttons */}
                <div
                    className={`flex gap-1 transition-opacity duration-200 ${isHovered ? 'opacity-100' : 'opacity-0'}`}
                >
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineEye />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onView?.(control)
                        }}
                        title="View Details"
                    />
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineClipboardCheck />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onViewTasks?.(control)
                        }}
                        title="View Tasks"
                    />
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlinePhotograph />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onViewEvidence?.(control)
                        }}
                        title="View Evidence"
                    />
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineCog />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onEdit?.(control)
                        }}
                        title="Settings"
                    />
                </div>
            </div>
        </Card>
    )
}

export default ControlCard
