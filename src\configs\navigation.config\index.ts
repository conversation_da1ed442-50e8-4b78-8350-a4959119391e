import { NAV_ITEM_TYPE_ITEM } from '@/constants/navigation.constant'

import type { NavigationTree } from '@/@types/navigation'

const navigationConfig: NavigationTree[] = [
    {
        key: 'compliance-dashboard',
        path: '/dashboard/compliance-dashboard',
        title: 'Dashboard',
        translateKey: 'nav.dashboard',
        icon: 'dashboard',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'framework-management',
        path: '/dashboard/framework-management',
        title: 'Frameworks',
        translateKey: 'nav.frameworks',
        icon: 'frameworks',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'control-hub',
        path: '/dashboard/control-hub',
        title: 'Controls',
        translateKey: 'nav.controls',
        icon: 'controls',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'task-engine',
        path: '/dashboard/task-engine',
        title: 'Tasks',
        translateKey: 'nav.tasks',
        icon: 'tasks',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'risk-management',
        path: '/dashboard/risk-management',
        title: 'Risks',
        translateKey: 'nav.risks',
        icon: 'risks',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'vendor-management',
        path: '/dashboard/vendor-management',
        title: 'Vendors',
        translateKey: 'nav.vendors',
        icon: 'vendors',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'policy-tracker',
        path: '/dashboard/policy-tracker',
        title: 'Policies',
        translateKey: 'nav.policies',
        icon: 'policies',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'asset-register',
        path: '/dashboard/asset-register',
        title: 'Assets',
        translateKey: 'nav.assets',
        icon: 'assets',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'audit-evidence',
        path: '/dashboard/audit-evidence',
        title: 'Evidence',
        translateKey: 'nav.evidence',
        icon: 'evidence',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'admin-settings',
        path: '/admin/settings',
        title: 'Admin Settings',
        translateKey: 'nav.adminSettings',
        icon: 'adminSettings',
        type: NAV_ITEM_TYPE_ITEM,
        authority: ['admin'],
        subMenu: [],
    },
]

export default navigationConfig
