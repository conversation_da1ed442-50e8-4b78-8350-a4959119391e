import { NAV_ITEM_TYPE_ITEM } from '@/constants/navigation.constant'

import type { NavigationTree } from '@/@types/navigation'

const navigationConfig: NavigationTree[] = [
    {
        key: 'compliance-dashboard',
        path: '/dashboard/compliance-dashboard',
        title: 'Dashboard',
        translateKey: 'nav.dashboard',
        icon: 'dashboard',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'framework-management',
        path: '/dashboard/framework-management',
        title: 'Frameworks',
        translateKey: 'nav.frameworks',
        icon: 'frameworks',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'admin-settings',
        path: '/admin/settings',
        title: 'Admin Settings',
        translateKey: 'nav.adminSettings',
        icon: 'adminSettings',
        type: NAV_ITEM_TYPE_ITEM,
        authority: ['admin'],
        subMenu: [],
    },
]

export default navigationConfig
