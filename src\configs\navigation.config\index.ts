import { NAV_ITEM_TYPE_ITEM, NAV_ITEM_TYPE_COLLAPSE } from '@/constants/navigation.constant'

import type { NavigationTree } from '@/@types/navigation'

const navigationConfig: NavigationTree[] = [
    {
        key: 'dashboard',
        path: '/dashboard',
        title: 'Dashboard',
        translateKey: 'nav.dashboard',
        icon: 'dashboard',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'frameworks',
        path: '/dashboard/frameworks',
        title: 'Frameworks',
        translateKey: 'nav.frameworks',
        icon: 'frameworks',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [],
        subMenu: [
            {
                key: 'frameworks-overview',
                path: '/dashboard/frameworks',
                title: 'Overview',
                translateKey: 'nav.frameworks.overview',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'pci-dss',
                path: '/dashboard/frameworks/pci-dss',
                title: 'PCI DSS',
                translateKey: 'nav.frameworks.pciDss',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
        ],
    },
    {
        key: 'admin-settings',
        path: '/admin/settings',
        title: 'Admin Settings',
        translateKey: 'nav.adminSettings',
        icon: 'adminSettings',
        type: NAV_ITEM_TYPE_ITEM,
        authority: ['admin'],
        subMenu: [],
    },
]

export default navigationConfig
