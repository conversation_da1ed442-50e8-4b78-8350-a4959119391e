# 🏗️ Modular Compliance Platform Architecture

This document outlines the modular micro front-end architecture for your PCI DSS 4.0.1 compliance platform.

## 📁 Project Structure

```
src/
├── shared/                     # Shared infrastructure
│   ├── types/                  # Common TypeScript types
│   ├── components/             # Reusable UI components
│   ├── hooks/                  # Shared React hooks
│   ├── constants/              # Application constants
│   ├── utils/                  # Utility functions
│   └── index.ts               # Shared exports
│
├── modules/                    # Feature modules (micro front-ends)
│   ├── framework-management/   # Manage compliance frameworks
│   ├── control-hub/           # Centralized control management
│   ├── task-engine/           # Recurring task management
│   ├── compliance-dashboard/   # High-level insights
│   ├── risk-management/       # Risk tracking
│   ├── vendor-management/     # Third-party vendor oversight
│   ├── policy-tracker/        # Policy management
│   ├── asset-register/        # Asset inventory
│   └── audit-evidence/        # Evidence collection
│
└── app/(protected-pages)/dashboard/  # Next.js App Router
    ├── framework-management/
    ├── control-hub/
    ├── task-engine/
    └── [other-modules]/
```

## 🧩 Module Structure

Each module follows this consistent structure:

```
src/modules/[module-name]/
├── types.ts                   # Module-specific types
├── components/                # Module components
│   ├── [Component].tsx
│   └── index.ts              # Component exports
├── hooks/                     # Module-specific hooks
│   ├── use[Module].ts
│   └── index.ts              # Hook exports
├── pages/                     # Module pages
│   └── [Module]Page.tsx
└── index.ts                  # Module exports
```

## 🚀 Implemented Modules

### 1. Framework Management (`/dashboard/framework-management`)

- **Purpose**: Manage compliance frameworks like PCI DSS 4.0.1
- **Features**:
    - Framework catalog with filtering and search
    - Progress tracking and completion rates
    - Framework cards with status indicators
    - Integration with controls and tasks

### 2. Task Engine (`/dashboard/task-engine`)

- **Purpose**: Manage recurring security/compliance tasks
- **Features**:
    - Recurring task creation (daily/weekly/monthly/quarterly/annually)
    - Task assignment and ownership
    - Due date tracking and overdue alerts
    - CSV export functionality
    - Grid and list view modes
    - Task filtering and categorization

### 3. Control Hub (`/dashboard/control-hub`)

- **Purpose**: Centralized control management across frameworks
- **Features**:
    - Control listing with framework association
    - Status tracking and evidence readiness
    - Risk level indicators
    - Control-to-task mapping

### 4. Compliance Dashboard (`/dashboard/compliance-dashboard`)

- **Purpose**: High-level insights and metrics
- **Features**:
    - Framework completion metrics
    - Task and risk overviews
    - Progress tracking
    - Team workload visualization

## 🔧 Shared Infrastructure

### Types (`src/shared/types/compliance.ts`)

- **Framework**: Core framework structure
- **Control**: Individual control requirements
- **Task**: Task management with recurrence
- **Risk**: Risk assessment and tracking
- **Evidence**: Evidence collection and approval
- **Vendor**: Third-party vendor management
- **Policy**: Policy tracking and acknowledgments
- **Asset**: Asset inventory and classification

### Components (`src/shared/components/`)

- **StatusBadge**: Consistent status indicators
- **FilterBar**: Advanced filtering with search
- **[More components to be added]**

### Hooks (`src/shared/hooks/`)

- **useSupabase**: Database connection and queries
- **useSupabaseQuery**: Generic data fetching
- **useSupabaseMutation**: Create, update, delete operations
- **Specialized hooks**: useFrameworks, useTasks, useControls, etc.

### Constants (`src/shared/constants/compliance.ts`)

- Framework categories, regions, statuses
- Control statuses and risk levels
- Task types, frequencies, priorities
- PCI DSS 4.0.1 specific requirements

### Utilities (`src/shared/utils/compliance.ts`)

- Progress calculation functions
- Risk scoring algorithms
- Date formatting and task scheduling
- CSV export functionality
- Compliance metrics calculations

## 🗄️ Database Schema

The platform uses Supabase with PostgreSQL. Key tables:

- **frameworks**: Compliance framework definitions
- **controls**: Individual control requirements
- **tasks**: Task management with recurrence rules
- **risks**: Risk assessment and mitigation
- **vendors**: Third-party vendor tracking
- **policies**: Policy management and acknowledgments
- **assets**: Asset inventory and classification
- **evidence**: Evidence collection and approval

See `database-schema.sql` for the complete schema.

## 🔐 Security & Access Control

- **Row Level Security (RLS)**: Organization-based data isolation
- **Authentication**: Integrated with Supabase Auth
- **Authorization**: Role-based access control (to be implemented)
- **Data Validation**: Zod schemas for type safety

## 🎨 Design Principles

### Manual-First Approach

- No automated evidence collection
- UI-centric design for clarity and control
- Manual task creation and management
- Human-driven compliance workflows

### Modular Architecture

- Each module is self-contained
- No tight coupling between modules
- Shared infrastructure through `/src/shared/`
- Independent deployment capability

### Performance & Scalability

- Files kept under 500 LOC
- Lazy loading of modules
- Efficient database queries with indexes
- Optimistic UI updates

## 🚦 Getting Started

### 1. Database Setup

```sql
-- Run the database schema
psql -h your-supabase-host -U postgres -d postgres -f database-schema.sql
```

### 2. Environment Variables

```env
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
```

### 3. Development

```bash
npm run dev
```

### 4. Navigation

- Visit `/dashboard/framework-management` for framework management
- Visit `/dashboard/task-engine` for task management
- Visit `/dashboard/control-hub` for control management
- Visit `/dashboard/compliance-dashboard` for overview

## 🔄 Module Integration

### Adding New Modules

1. Create module directory in `/src/modules/`
2. Follow the standard module structure
3. Add route in `/src/app/(protected-pages)/dashboard/`
4. Update navigation in `/src/configs/navigation.config/`
5. Export module from `/src/modules/[module-name]/index.ts`

### Inter-Module Communication

- Use shared types from `/src/shared/types/`
- Leverage shared hooks for data access
- Pass data through URL parameters or global state
- Avoid direct module dependencies

## 📊 Current Implementation Status

✅ **Completed**:

- Shared infrastructure (types, components, hooks, utils)
- Framework Management module
- Task Engine module (with recurring tasks)
- Control Hub module (basic structure)
- Compliance Dashboard module (basic structure)
- Database schema design
- Navigation integration

🚧 **In Progress**:

- Risk Management module
- Vendor Management module
- Policy Tracker module
- Asset Register module
- Audit Evidence module

🔮 **Future Enhancements**:

- AI assistant for control explanations
- Smart control suggestions
- Slack/Teams integration
- Multi-framework mapping
- Control exemption workflows
- Advanced reporting and analytics

## 🎯 PCI DSS 4.0.1 Focus

The platform is specifically designed for PCI DSS 4.0.1 compliance:

- **12 Core Requirements**: Mapped to control structure
- **264 Controls**: Detailed control tracking
- **Recurring Tasks**: Quarterly scans, monthly reviews
- **Evidence Collection**: Manual screenshot and document upload
- **Risk Assessment**: Cardholder data environment focus
- **Vendor Management**: Third-party service provider tracking

## 🤝 Contributing

When adding new features:

1. Follow the modular architecture principles
2. Keep files under 500 lines
3. Use shared infrastructure where possible
4. Maintain type safety with TypeScript
5. Add appropriate tests
6. Update documentation

## 📞 Support

For questions about the modular architecture or implementation details, refer to:

- Module-specific README files (to be added)
- Shared component documentation
- Database schema comments
- TypeScript type definitions
