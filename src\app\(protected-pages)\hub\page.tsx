'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import Tabs from '@/components/ui/Tabs'
import {
    PiHouseLineDuotone,
    PiUserCircleDuotone,
    PiPuzzlePieceDuotone,
    PiCreditCardDuotone,
    PiRoadHorizonDuotone,
    PiStackDuotone,
} from 'react-icons/pi'

// Tab components
import OverviewTab from './tabs/OverviewTab'
import FrameworksTab from './tabs/FrameworksTab'
import MySubscriptionTab from './tabs/MySubscriptionTab'
import AddOnsTab from './tabs/AddOnsTab'
import BillingUsageTab from './tabs/BillingUsageTab'
import RoadmapTab from './tabs/RoadmapTab'

const HubPage = () => {
    const [activeTab, setActiveTab] = useState('overview')
    const { user, loading } = useAuth()
    const router = useRouter()

    // For now, we'll consider all authenticated users as admins
    // TODO: Implement proper role checking with Supabase user metadata
    const isAuthenticated = !!user
    const isAdmin = isAuthenticated // Temporary: all authenticated users can access hub

    useEffect(() => {
        // Redirect non-admin users to the dashboard
        if (!loading && isAuthenticated && !isAdmin) {
            router.push('/dashboard/compliance-dashboard')
        }
    }, [loading, isAuthenticated, isAdmin, router])

    // Show loading state
    if (loading) {
        return (
            <div className="p-6">
                <div className="text-center">
                    <p>Loading...</p>
                </div>
            </div>
        )
    }

    // Don't render anything for non-admin users while redirecting
    if (isAuthenticated && !isAdmin) {
        return (
            <div className="p-6">
                <div className="text-center">
                    <p>Redirecting to dashboard...</p>
                </div>
            </div>
        )
    }

    return (
        <div className="p-6">
            <div className="mb-6">
                <h4 className="text-2xl font-bold mb-2">Hub</h4>
                <p className="text-gray-500 dark:text-gray-400">
                    Your compliance control center - manage frameworks, add-ons,
                    and subscriptions
                </p>
            </div>

            <Tabs
                value={activeTab}
                onChange={(val) => setActiveTab(val as string)}
            >
                <Tabs.TabList>
                    <Tabs.TabNav value="overview" icon={<PiHouseLineDuotone />}>
                        Overview
                    </Tabs.TabNav>
                    <Tabs.TabNav value="frameworks" icon={<PiStackDuotone />}>
                        Frameworks
                    </Tabs.TabNav>
                    <Tabs.TabNav
                        value="subscription"
                        icon={<PiUserCircleDuotone />}
                    >
                        My Subscription
                    </Tabs.TabNav>
                    <Tabs.TabNav value="addons" icon={<PiPuzzlePieceDuotone />}>
                        Add-Ons
                    </Tabs.TabNav>
                    <Tabs.TabNav value="billing" icon={<PiCreditCardDuotone />}>
                        Billing & Usage
                    </Tabs.TabNav>
                    <Tabs.TabNav
                        value="roadmap"
                        icon={<PiRoadHorizonDuotone />}
                    >
                        Roadmap
                    </Tabs.TabNav>
                </Tabs.TabList>
                <div className="mt-4">
                    <Tabs.TabContent value="overview">
                        <OverviewTab />
                    </Tabs.TabContent>
                    <Tabs.TabContent value="frameworks">
                        <FrameworksTab />
                    </Tabs.TabContent>
                    <Tabs.TabContent value="subscription">
                        <MySubscriptionTab />
                    </Tabs.TabContent>
                    <Tabs.TabContent value="addons">
                        <AddOnsTab />
                    </Tabs.TabContent>
                    <Tabs.TabContent value="billing">
                        <BillingUsageTab />
                    </Tabs.TabContent>
                    <Tabs.TabContent value="roadmap">
                        <RoadmapTab />
                    </Tabs.TabContent>
                </div>
            </Tabs>
        </div>
    )
}

export default HubPage
