'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import StatusBadge from '../components/StatusBadge'
import Input from '@/components/ui/Input'
import Select from '@/components/ui/Select'
import { PiMagnifyingGlassDuotone, PiStackDuotone } from 'react-icons/pi'

const FrameworksTab = () => {
    const [searchTerm, setSearchTerm] = useState('')
    const [industryFilter, setIndustryFilter] = useState<string | null>(null)
    const [regionFilter, setRegionFilter] = useState<string | null>(null)

    // Extract unique values for filters
    const industries = [...new Set(frameworks.map((item) => item.industry))]
    const regions = [...new Set(frameworks.map((item) => item.region))]

    // Filter items based on search and filters
    const filteredItems = frameworks.filter((item) => {
        const matchesSearch =
            item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.description.toLowerCase().includes(searchTerm.toLowerCase())
        const matchesIndustry =
            !industryFilter || item.industry === industryFilter
        const matchesRegion = !regionFilter || item.region === regionFilter

        return matchesSearch && matchesIndustry && matchesRegion
    })

    // Get color for industry badge
    const getIndustryColor = (industry: string) => {
        switch (industry) {
            case 'Cybersecurity':
                return 'bg-blue-100 text-blue-600 border border-blue-200'
            case 'ESG':
                return 'bg-green-100 text-green-600 border border-green-200'
            case 'AI Ethics':
                return 'bg-purple-100 text-purple-600 border border-purple-200'
            case 'Strategic':
                return 'bg-amber-100 text-amber-600 border border-amber-200'
            case 'Financial':
                return 'bg-emerald-100 text-emerald-600 border border-emerald-200'
            default:
                return 'bg-gray-100 text-gray-600 border border-gray-200'
        }
    }

    // Get CTA button based on item status
    const getCtaButton = (item: Framework) => {
        switch (item.status) {
            case 'available':
                return (
                    <Button
                        size="sm"
                        variant="solid"
                        className="bg-emerald-500 hover:bg-emerald-600"
                    >
                        Activate
                    </Button>
                )
            case 'preview':
                return (
                    <Button size="sm" variant="default">
                        Preview
                    </Button>
                )
            case 'upgrade':
                return (
                    <Button
                        size="sm"
                        variant="default"
                        className="text-amber-500 border-amber-300"
                    >
                        Upgrade Required
                    </Button>
                )
            default:
                return null
        }
    }

    return (
        <div>
            {/* Filters */}
            <Card className="mb-4">
                <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-grow">
                        <Input
                            prefix={
                                <PiMagnifyingGlassDuotone className="text-lg" />
                            }
                            placeholder="Search frameworks..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </div>
                    <div className="flex flex-wrap gap-3">
                        <Select
                            className="min-w-[150px]"
                            placeholder="Industry"
                            options={industries.map((industry) => ({
                                value: industry,
                                label: industry,
                            }))}
                            value={
                                industryFilter
                                    ? {
                                          value: industryFilter,
                                          label: industryFilter,
                                      }
                                    : null
                            }
                            onChange={(option) =>
                                setIndustryFilter(option?.value || null)
                            }
                            isClearable
                        />
                        <Select
                            className="min-w-[150px]"
                            placeholder="Region"
                            options={regions.map((region) => ({
                                value: region,
                                label: region,
                            }))}
                            value={
                                regionFilter
                                    ? {
                                          value: regionFilter,
                                          label: regionFilter,
                                      }
                                    : null
                            }
                            onChange={(option) =>
                                setRegionFilter(option?.value || null)
                            }
                            isClearable
                        />
                    </div>
                </div>
            </Card>

            {/* Frameworks Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredItems.map((item) => (
                    <Card key={item.id} className="h-full">
                        <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center">
                                <span className="text-2xl mr-3">
                                    <PiStackDuotone className="text-primary" />
                                </span>
                                <div>
                                    <h6 className="font-semibold">
                                        {item.name}
                                    </h6>
                                    <StatusBadge
                                        className={getIndustryColor(
                                            item.industry,
                                        )}
                                    >
                                        {item.industry}
                                    </StatusBadge>
                                </div>
                            </div>
                            <StatusBadge className="bg-gray-100 text-gray-600 border border-gray-200">
                                {item.region}
                            </StatusBadge>
                        </div>
                        <p className="text-sm text-gray-500 mb-4 line-clamp-2">
                            {item.description}
                        </p>
                        <div className="flex items-center justify-between mt-auto">
                            <div>
                                <StatusBadge className="bg-blue-100 text-blue-600 border border-blue-200">
                                    {item.plan}
                                </StatusBadge>
                            </div>
                            {getCtaButton(item)}
                        </div>
                    </Card>
                ))}
            </div>

            {/* Empty State */}
            {filteredItems.length === 0 && (
                <div className="text-center py-12">
                    <h5 className="font-semibold mb-2">No frameworks found</h5>
                    <p className="text-gray-500 dark:text-gray-400 mb-4">
                        Try adjusting your search or filters to find what you
                        need.
                    </p>
                </div>
            )}
        </div>
    )
}

// Types
interface Framework {
    id: number
    name: string
    industry: string
    region: string
    plan: string
    description: string
    status: 'available' | 'preview' | 'upgrade'
}

// Sample data - Only frameworks
const frameworks: Framework[] = [
    {
        id: 1,
        name: 'ISO 14001',
        industry: 'ESG',
        region: 'Global',
        plan: 'Pro',
        description:
            'Environmental management system standard for organizations.',
        status: 'available',
    },
    {
        id: 2,
        name: 'Basel III',
        industry: 'Financial',
        region: 'Global',
        plan: 'Enterprise',
        description:
            'Global regulatory framework for more resilient banks and banking systems.',
        status: 'upgrade',
    },
    {
        id: 3,
        name: 'GDPR',
        industry: 'Cybersecurity',
        region: 'EU',
        plan: 'Growth',
        description:
            'General Data Protection Regulation for data privacy in the EU.',
        status: 'available',
    },
    {
        id: 4,
        name: 'OKRs',
        industry: 'Strategic',
        region: 'Global',
        plan: 'Starter',
        description:
            'Objectives and Key Results framework for goal setting and tracking.',
        status: 'available',
    },
    {
        id: 5,
        name: 'EU AI Act',
        industry: 'AI Ethics',
        region: 'EU',
        plan: 'Enterprise',
        description:
            'European Union regulations for artificial intelligence systems.',
        status: 'preview',
    },
    {
        id: 6,
        name: 'ISO 27001',
        industry: 'Cybersecurity',
        region: 'Global',
        plan: 'Growth',
        description: 'Information security management system standard.',
        status: 'available',
    },
    {
        id: 7,
        name: 'SOC 2',
        industry: 'Cybersecurity',
        region: 'US',
        plan: 'Growth',
        description:
            'Service Organization Control 2 for security, availability, processing integrity, confidentiality, and privacy.',
        status: 'available',
    },
    {
        id: 8,
        name: 'NIST CSF',
        industry: 'Cybersecurity',
        region: 'US',
        plan: 'Pro',
        description:
            'National Institute of Standards and Technology Cybersecurity Framework.',
        status: 'available',
    },
]

export default FrameworksTab
