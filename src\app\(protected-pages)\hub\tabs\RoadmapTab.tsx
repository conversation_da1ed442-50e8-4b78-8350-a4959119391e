'use client'

import React, { useState } from 'react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import StatusBadge from '../components/StatusBadge'
import Input from '@/components/ui/Input'
import Tabs from '@/components/ui/Tabs'
import {
    PiClockDuotone,
    PiHammerDuotone,
    PiCheckCircleDuotone,
    PiThumbsUpDuotone,
    PiStackDuotone,
    PiPuzzlePieceDuotone,
    PiPlusDuotone,
} from 'react-icons/pi'

const RoadmapTab = () => {
    const [activeTab, setActiveTab] = useState('planned')
    const [requestInput, setRequestInput] = useState('')
    const [upvotedItems, setUpvotedItems] = useState<number[]>([])

    const toggleUpvote = (id: number) => {
        if (upvotedItems.includes(id)) {
            setUpvotedItems(upvotedItems.filter((item) => item !== id))
        } else {
            setUpvotedItems([...upvotedItems, id])
        }
    }

    // Filter roadmap items based on active tab
    const filteredItems = roadmapItems.filter(
        (item) => item.status === activeTab,
    )

    // Get status icon
    const getStatusIcon = (status: string): React.ReactElement | null => {
        switch (status) {
            case 'planned':
                return <PiClockDuotone className="text-blue-500" />
            case 'building':
                return <PiHammerDuotone className="text-amber-500" />
            case 'released':
                return <PiCheckCircleDuotone className="text-emerald-500" />
            default:
                return null
        }
    }

    // Get status badge color
    const getStatusColor = (status: string) => {
        switch (status) {
            case 'planned':
                return 'bg-blue-100 text-blue-600 border border-blue-200'
            case 'building':
                return 'bg-amber-100 text-amber-600 border border-amber-200'
            case 'released':
                return 'bg-emerald-100 text-emerald-600 border border-emerald-200'
            default:
                return 'bg-gray-100 text-gray-600 border border-gray-200'
        }
    }

    // Get type icon
    const getTypeIcon = (type: string): React.ReactElement | null => {
        switch (type) {
            case 'Framework':
                return <PiStackDuotone className="text-primary" />
            case 'Add-On':
                return <PiPuzzlePieceDuotone className="text-blue-500" />
            case 'Feature':
                return <PiPuzzlePieceDuotone className="text-purple-500" />
            default:
                return null
        }
    }

    // Handle request submission
    const handleRequestSubmit = () => {
        if (requestInput.trim()) {
            alert(`Request submitted: ${requestInput}`)
            setRequestInput('')
        }
    }

    return (
        <div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <div className="lg:col-span-2">
                    <Card>
                        <Tabs
                            value={activeTab}
                            onChange={(val) => setActiveTab(val as string)}
                        >
                            <Tabs.TabList>
                                <Tabs.TabNav
                                    value="planned"
                                    icon={<PiClockDuotone />}
                                >
                                    Planned
                                </Tabs.TabNav>
                                <Tabs.TabNav
                                    value="building"
                                    icon={<PiHammerDuotone />}
                                >
                                    Building
                                </Tabs.TabNav>
                                <Tabs.TabNav
                                    value="released"
                                    icon={<PiCheckCircleDuotone />}
                                >
                                    Released
                                </Tabs.TabNav>
                            </Tabs.TabList>

                            <div className="mt-4">
                                <Tabs.TabContent value="planned">
                                    <div className="space-y-4">
                                        {filteredItems.map((item) => (
                                            <RoadmapItem
                                                key={item.id}
                                                item={item}
                                                isUpvoted={upvotedItems.includes(
                                                    item.id,
                                                )}
                                                onUpvote={() =>
                                                    toggleUpvote(item.id)
                                                }
                                                getStatusIcon={getStatusIcon}
                                                getStatusColor={getStatusColor}
                                                getTypeIcon={getTypeIcon}
                                            />
                                        ))}
                                    </div>
                                </Tabs.TabContent>
                                <Tabs.TabContent value="building">
                                    <div className="space-y-4">
                                        {filteredItems.map((item) => (
                                            <RoadmapItem
                                                key={item.id}
                                                item={item}
                                                isUpvoted={upvotedItems.includes(
                                                    item.id,
                                                )}
                                                onUpvote={() =>
                                                    toggleUpvote(item.id)
                                                }
                                                getStatusIcon={getStatusIcon}
                                                getStatusColor={getStatusColor}
                                                getTypeIcon={getTypeIcon}
                                            />
                                        ))}
                                    </div>
                                </Tabs.TabContent>
                                <Tabs.TabContent value="released">
                                    <div className="space-y-4">
                                        {filteredItems.map((item) => (
                                            <RoadmapItem
                                                key={item.id}
                                                item={item}
                                                isUpvoted={upvotedItems.includes(
                                                    item.id,
                                                )}
                                                onUpvote={() =>
                                                    toggleUpvote(item.id)
                                                }
                                                getStatusIcon={getStatusIcon}
                                                getStatusColor={getStatusColor}
                                                getTypeIcon={getTypeIcon}
                                            />
                                        ))}
                                    </div>
                                </Tabs.TabContent>
                            </div>
                        </Tabs>
                    </Card>
                </div>

                <div className="space-y-4">
                    {/* Request Feature or Framework */}
                    <Card>
                        <h5 className="font-bold text-lg mb-4">
                            Request Feature or Framework
                        </h5>

                        <div className="space-y-4">
                            <Input
                                placeholder="Describe what you'd like to see..."
                                value={requestInput}
                                onChange={(e) =>
                                    setRequestInput(e.target.value)
                                }
                                textArea
                                rows={4}
                            />

                            <Button
                                block
                                variant="solid"
                                className="bg-emerald-500 hover:bg-emerald-600"
                                icon={<PiPlusDuotone />}
                                onClick={handleRequestSubmit}
                                disabled={!requestInput.trim()}
                            >
                                Submit Request
                            </Button>
                        </div>
                    </Card>

                    {/* Coming Soon */}
                    <Card>
                        <h5 className="font-bold text-lg mb-4">Coming Soon</h5>

                        <div className="space-y-3">
                            {comingSoonItems.map((item, index) => (
                                <div
                                    key={index}
                                    className={`pb-3 ${index !== comingSoonItems.length - 1 ? 'border-b border-gray-200 dark:border-gray-700' : ''}`}
                                >
                                    <div className="flex items-start">
                                        <span className="text-xl mr-3 mt-0.5">
                                            {getTypeIcon(item.type)}
                                        </span>
                                        <div>
                                            <div className="font-medium">
                                                {item.name}
                                            </div>
                                            <div className="flex items-center mt-1">
                                                <StatusBadge
                                                    className={getStatusColor(
                                                        'planned',
                                                    )}
                                                >
                                                    Q{item.quarter} {item.year}
                                                </StatusBadge>
                                                <StatusBadge className="ml-2 bg-gray-100 text-gray-600 border border-gray-200">
                                                    {item.type}
                                                </StatusBadge>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </Card>
                </div>
            </div>
        </div>
    )
}

// Roadmap Item Component
interface RoadmapItemProps {
    item: RoadmapItem
    isUpvoted: boolean
    onUpvote: () => void
    getStatusIcon: (status: string) => React.ReactElement | null
    getStatusColor: (status: string) => string
    getTypeIcon: (type: string) => React.ReactElement | null
}

const RoadmapItem = ({
    item,
    isUpvoted,
    onUpvote,
    getStatusIcon,
    getStatusColor,
    getTypeIcon,
}: RoadmapItemProps) => {
    return (
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-start justify-between mb-3">
                <div className="flex items-start">
                    <span className="text-2xl mr-3 mt-0.5">
                        {getTypeIcon(item.type)}
                    </span>
                    <div>
                        <h6 className="font-semibold">{item.name}</h6>
                        <div className="flex items-center mt-1">
                            <StatusBadge
                                className={getStatusColor(item.status)}
                            >
                                <span className="flex items-center">
                                    {getStatusIcon(item.status)}
                                    <span className="ml-1">
                                        {item.status.charAt(0).toUpperCase() +
                                            item.status.slice(1)}
                                    </span>
                                </span>
                            </StatusBadge>
                            <StatusBadge className="ml-2 bg-gray-100 text-gray-600 border border-gray-200">
                                {item.type}
                            </StatusBadge>
                            {item.eta && (
                                <span className="ml-2 text-xs text-gray-500">
                                    ETA: {item.eta}
                                </span>
                            )}
                        </div>
                    </div>
                </div>
                {item.status !== 'released' && (
                    <Button
                        size="xs"
                        variant={isUpvoted ? 'solid' : 'default'}
                        className={
                            isUpvoted ? 'bg-blue-500 hover:bg-blue-600' : ''
                        }
                        icon={<PiThumbsUpDuotone />}
                        onClick={onUpvote}
                    >
                        {item.upvotes + (isUpvoted ? 1 : 0)}
                    </Button>
                )}
            </div>
            <p className="text-sm text-gray-500 mb-2">{item.description}</p>
            {item.status === 'released' && (
                <div className="text-xs text-gray-500">
                    Released: {item.releaseDate}
                </div>
            )}
        </div>
    )
}

// Types
interface RoadmapItem {
    id: number
    name: string
    type: 'Framework' | 'Add-On' | 'Feature'
    description: string
    status: 'planned' | 'building' | 'released'
    upvotes: number
    eta?: string
    releaseDate?: string
}

// Sample data
const roadmapItems: RoadmapItem[] = [
    {
        id: 1,
        name: 'CMMC 2.0',
        type: 'Framework',
        description:
            'Cybersecurity Maturity Model Certification for defense contractors.',
        status: 'planned',
        upvotes: 42,
        eta: 'Q1 2024',
    },
    {
        id: 2,
        name: 'SASB Standards',
        type: 'Framework',
        description:
            'Sustainability Accounting Standards Board reporting framework.',
        status: 'planned',
        upvotes: 28,
        eta: 'Q2 2024',
    },
    {
        id: 3,
        name: 'MiFID II',
        type: 'Framework',
        description:
            'Markets in Financial Instruments Directive for EU financial markets.',
        status: 'building',
        upvotes: 35,
        eta: 'Q1 2024',
    },
    {
        id: 4,
        name: 'Document AI Processing',
        type: 'Feature',
        description:
            'AI-powered document analysis for automated evidence collection.',
        status: 'building',
        upvotes: 56,
        eta: 'Q1 2024',
    },
    {
        id: 5,
        name: 'ISO 27001:2022',
        type: 'Framework',
        description:
            'Updated version of the information security management standard.',
        status: 'released',
        upvotes: 0,
        releaseDate: 'Dec 10, 2023',
    },
    {
        id: 6,
        name: 'Multi-framework Control Mapping',
        type: 'Feature',
        description:
            'Map controls across multiple frameworks to reduce duplicate work.',
        status: 'released',
        upvotes: 0,
        releaseDate: 'Nov 15, 2023',
    },
]

const comingSoonItems = [
    {
        name: 'NIST CSF 2.0',
        type: 'Framework',
        quarter: 1,
        year: 2024,
    },
    {
        name: 'SOC 2 Type II',
        type: 'Framework',
        quarter: 1,
        year: 2024,
    },
    {
        name: 'Evidence Collection API',
        type: 'Feature',
        quarter: 2,
        year: 2024,
    },
    {
        name: 'ISO 42001 (AI Management)',
        type: 'Framework',
        quarter: 2,
        year: 2024,
    },
    {
        name: 'Automated Control Testing',
        type: 'Add-On',
        quarter: 3,
        year: 2024,
    },
]

export default RoadmapTab
