'use client'

import { useState } from 'react'
import UserList from '../components/UserList'
import AddUserModal from '../components/AddUserModal'
import EditUserModal from '../components/EditUserModal'
import UserPermissionsModal from '../components/UserPermissionsModal'
import { useMockUsers, useUserMutations } from '../hooks'
import { User, CreateUserRequest, UpdateUserRequest } from '../types'
import { exportToCSV } from '@/shared/utils/compliance'

const UserManagementPage = () => {
    const { data: users, loading, error, refetch } = useMockUsers()
    const {
        createUser,
        updateUser,
        deleteUser,
        changeUserRole,
        activateUser,
        deactivateUser,
        loading: mutationLoading,
    } = useUserMutations()

    const [showAddModal, setShowAddModal] = useState(false)
    const [showEditModal, setShowEditModal] = useState(false)
    const [showPermissionsModal, setShowPermissionsModal] = useState(false)
    const [selectedUser, setSelectedUser] = useState<User | null>(null)

    // Mock current user ID - in real app this would come from auth context
    const currentUserId = 'user-1'

    const handleAddUser = async (userData: CreateUserRequest) => {
        try {
            await createUser(userData)
            refetch()
            setShowAddModal(false)
        } catch (error) {
            console.error('Error adding user:', error)
        }
    }

    const handleEditUser = (user: User) => {
        setSelectedUser(user)
        setShowEditModal(true)
    }

    const handleUpdateUser = async (
        id: string,
        userData: UpdateUserRequest,
    ) => {
        try {
            await updateUser(id, userData)
            refetch()
            setShowEditModal(false)
            setSelectedUser(null)
        } catch (error) {
            console.error('Error updating user:', error)
        }
    }

    const handleDeleteUser = async (user: User) => {
        if (
            window.confirm(
                `Are you sure you want to delete ${user.firstName} ${user.lastName}?`,
            )
        ) {
            try {
                await deleteUser(user.id)
                refetch()
            } catch (error) {
                console.error('Error deleting user:', error)
            }
        }
    }

    const handleChangeRole = async (user: User, role: 'admin' | 'user') => {
        if (
            window.confirm(
                `Change ${user.firstName} ${user.lastName}'s role to ${role}?`,
            )
        ) {
            try {
                await changeUserRole(user.id, role)
                refetch()
            } catch (error) {
                console.error('Error changing user role:', error)
            }
        }
    }

    const handleChangeStatus = async (
        user: User,
        status: 'active' | 'inactive' | 'suspended',
    ) => {
        const action =
            status === 'active'
                ? 'activate'
                : status === 'inactive'
                  ? 'deactivate'
                  : 'suspend'

        if (
            window.confirm(
                `Are you sure you want to ${action} ${user.firstName} ${user.lastName}?`,
            )
        ) {
            try {
                if (status === 'active') {
                    await activateUser(user.id)
                } else {
                    await deactivateUser(user.id)
                }
                refetch()
            } catch (error) {
                console.error(`Error ${action}ing user:`, error)
            }
        }
    }

    const handleViewPermissions = (user: User) => {
        setSelectedUser(user)
        setShowPermissionsModal(true)
    }

    const handleSavePermissions = async (
        userId: string,
        permissions: string[],
    ) => {
        try {
            await updateUser(userId, { permissions })
            refetch()
            setShowPermissionsModal(false)
            setSelectedUser(null)
        } catch (error) {
            console.error('Error saving permissions:', error)
        }
    }

    const handleExportCSV = () => {
        const exportData = users.map((user) => ({
            'User ID': user.id,
            Email: user.email,
            Username: user.userName,
            'First Name': user.firstName || '',
            'Last Name': user.lastName || '',
            Role: user.role,
            Status: user.status,
            'Last Login': user.lastLogin || 'Never',
            'Created Date': user.createdAt,
            'Updated Date': user.updatedAt,
            'Permissions Count': user.permissions?.length || 0,
        }))

        exportToCSV(
            exportData,
            `users-export-${new Date().toISOString().split('T')[0]}`,
        )
    }

    if (error) {
        return (
            <div className="p-6">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h3 className="text-red-800 font-medium">
                        Error loading users
                    </h3>
                    <p className="text-red-600 text-sm mt-1">{error}</p>
                </div>
            </div>
        )
    }

    return (
        <div className="p-6">
            <UserList
                users={users}
                loading={loading}
                onEdit={handleEditUser}
                onDelete={handleDeleteUser}
                onChangeRole={handleChangeRole}
                onChangeStatus={handleChangeStatus}
                onViewPermissions={handleViewPermissions}
                onAddUser={() => setShowAddModal(true)}
                onExportCSV={handleExportCSV}
                currentUserId={currentUserId}
            />

            <AddUserModal
                isOpen={showAddModal}
                onClose={() => setShowAddModal(false)}
                onSubmit={handleAddUser}
                loading={mutationLoading}
            />

            <EditUserModal
                isOpen={showEditModal}
                onClose={() => {
                    setShowEditModal(false)
                    setSelectedUser(null)
                }}
                onSubmit={handleUpdateUser}
                user={selectedUser}
                loading={mutationLoading}
            />

            <UserPermissionsModal
                isOpen={showPermissionsModal}
                onClose={() => {
                    setShowPermissionsModal(false)
                    setSelectedUser(null)
                }}
                user={selectedUser}
                onSave={handleSavePermissions}
                loading={mutationLoading}
            />
        </div>
    )
}

export default UserManagementPage
