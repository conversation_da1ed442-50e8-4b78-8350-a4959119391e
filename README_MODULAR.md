# 🏗️ Modular Compliance Platform

A **manual-first, modular compliance management platform** built with Next.js, TypeScript, and Supabase. Specifically designed for **PCI DSS 4.0.1 compliance** with support for multiple frameworks. Features a micro front-end architecture where each compliance area is an independent, self-contained module.

## 🎯 **Design Philosophy**

### Manual-First Approach

- ❌ **No automated evidence collection** - Full control over compliance processes
- ✅ **UI-centric workflows** - Clear, intuitive interfaces for all compliance activities
- ✅ **Human-driven decisions** - Manual task creation, control implementation, and evidence upload
- ✅ **Simplified Vanta alternative** - Focus on essential compliance without complexity

### Modular Architecture

- 🧩 **Independent modules** - Each compliance area is self-contained
- 🔄 **No tight coupling** - Modules communicate through shared infrastructure
- 📁 **Domain-based organization** - Features grouped by business function
- 🚀 **Scalable development** - Add new modules without affecting existing ones

## 🏢 **Core Modules**

### 1. **Framework Management** (`/dashboard/framework-management`)

Manage compliance frameworks like PCI DSS 4.0.1, ISO 27001, GDPR

- Framework catalog with progress tracking
- 264 PCI DSS controls mapped and ready
- Status indicators and completion metrics

### 2. **Task Engine** (`/dashboard/task-engine`) - **CRITICAL**

Recurring security and compliance task management

- Quarterly vulnerability scans
- Monthly access reviews
- Annual policy updates
- Overdue tracking and notifications

### 3. **Control Hub** (`/dashboard/control-hub`)

Centralized control management across all frameworks

- Control status tracking
- Evidence readiness indicators
- Risk level assignments
- Framework cross-mapping

### 4. **Risk Management** (`/dashboard/risk-management`)

Business and security risk tracking

- Risk assessment matrix (likelihood × impact)
- Mitigation planning and tracking
- Control linkage and effectiveness

### 5. **Compliance Dashboard** (`/dashboard/compliance-dashboard`)

High-level insights and metrics

- Framework completion rates
- Overdue task alerts
- Risk exposure summary
- Team workload overview

## 🚀 **Additional Modules** (Ready to Implement)

- **Vendor Management**: Third-party risk assessment
- **Policy Tracker**: Policy versioning and acknowledgments
- **Asset Register**: Critical systems inventory
- **Audit Evidence**: Manual evidence collection and mapping

## 🌟 **Key Features**

### PCI DSS 4.0.1 Focused

- **12 Core Requirements** mapped to control structure
- **264 Controls** with detailed tracking
- **Quarterly Tasks** for vulnerability scanning
- **Monthly Reviews** for access management
- **Evidence Collection** for audit readiness

### Manual Control & Transparency

- **No Black Box** - Every process is visible and controllable
- **Manual Evidence Upload** - Screenshots, documents, certificates
- **Human Approval Workflows** - No automated compliance decisions
- **Clear Audit Trails** - Full visibility into all compliance activities

## 🚀 **Quick Start**

### 1. **Database Setup**

```bash
# Apply the database schema to your Supabase project
psql -h your-supabase-host -U postgres -d postgres -f database-schema.sql

# Or run the automated setup
cd scripts && npm install && npm run setup
```

### 2. **Environment Configuration**

```env
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key
```

### 3. **Development**

```bash
npm install
npm run dev
```

### 4. **Access Modules**

- **Dashboard**: `http://localhost:3000/dashboard/compliance-dashboard`
- **Frameworks**: `http://localhost:3000/dashboard/framework-management`
- **Tasks**: `http://localhost:3000/dashboard/task-engine`
- **Risks**: `http://localhost:3000/dashboard/risk-management`

## 📁 **Architecture Overview**

```
src/
├── shared/                     # Shared infrastructure
│   ├── types/                  # Common TypeScript types
│   ├── components/             # Reusable UI components
│   ├── hooks/                  # Shared React hooks
│   ├── constants/              # Application constants
│   └── utils/                  # Utility functions
│
├── modules/                    # Feature modules (micro front-ends)
│   ├── framework-management/   # ✅ Implemented
│   ├── task-engine/           # ✅ Implemented
│   ├── control-hub/           # ✅ Implemented
│   ├── risk-management/       # ✅ Implemented
│   ├── compliance-dashboard/   # ✅ Implemented
│   ├── vendor-management/     # 🚧 Partial
│   ├── policy-tracker/        # 📋 Planned
│   ├── asset-register/        # 📋 Planned
│   └── audit-evidence/        # 📋 Planned
│
└── app/(protected-pages)/dashboard/  # Next.js App Router
    ├── framework-management/
    ├── task-engine/
    ├── risk-management/
    └── [other-modules]/
```

## 🔧 **Technology Stack**

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS 4.0, Custom UI Components
- **Database**: Supabase (PostgreSQL) with Row Level Security
- **State Management**: Zustand (ready to implement)
- **Forms**: React Hook Form with Zod validation
- **Icons**: React Icons (Heroicons)
- **Charts**: React ApexCharts (for dashboard metrics)

## 🎯 **Implementation Status**

### ✅ **Completed**

- [x] Shared infrastructure (types, components, hooks, utils)
- [x] Framework Management module with filtering and progress tracking
- [x] Task Engine with recurring task support and CSV export
- [x] Risk Management with risk matrix and mitigation tracking
- [x] Compliance Dashboard with metrics and alerts
- [x] Database schema with RLS security
- [x] Navigation and routing structure

### 🚧 **In Progress**

- [ ] Vendor Management module (partially implemented)
- [ ] Control Hub module (basic structure ready)
- [ ] Authentication integration
- [ ] Real-time data synchronization

### 📋 **Planned**

- [ ] Policy Tracker module
- [ ] Asset Register module
- [ ] Audit Evidence module
- [ ] Advanced reporting and analytics
- [ ] Email notifications
- [ ] Slack/Teams integration

## 🔐 **Security Features**

- **Row Level Security (RLS)** - Organization-based data isolation
- **Type Safety** - Full TypeScript coverage
- **Input Validation** - Zod schemas for all data
- **Secure API Routes** - Protected endpoints
- **Environment Variables** - Secure configuration management

## 📊 **Sample Data Included**

The platform comes with realistic sample data for testing:

- **5 Frameworks**: PCI DSS 4.0.1, ISO 27001, GDPR, SOX, HIPAA
- **Sample Controls**: Mapped to PCI DSS requirements
- **Recurring Tasks**: Quarterly scans, monthly reviews
- **Risk Scenarios**: Cardholder data exposure, insider threats
- **Compliance Metrics**: Progress tracking and completion rates

## 🤝 **Contributing**

When adding new modules or features:

1. **Follow the modular architecture** - Keep modules independent
2. **Use shared infrastructure** - Leverage `/src/shared/` components and hooks
3. **Maintain type safety** - Full TypeScript coverage required
4. **Keep files under 500 LOC** - Break down large files into smaller components
5. **Add appropriate tests** - Unit tests for critical functionality
6. **Update documentation** - Keep README and architecture docs current

## 📚 **Documentation**

- **[Setup Guide](SETUP_GUIDE.md)** - Complete setup instructions
- **[Architecture Guide](MODULAR_ARCHITECTURE.md)** - Detailed architecture documentation
- **[Database Schema](database-schema.sql)** - Complete database structure

## 🆘 **Support**

For questions or issues:

1. Check the **Setup Guide** for common problems
2. Review the **Architecture Guide** for implementation details
3. Examine the **sample data** for expected data structures
4. Check browser console and terminal for error messages

## 📈 **Roadmap**

### Q1 2024

- [ ] Complete all 9 core modules
- [ ] Add user authentication and authorization
- [ ] Implement real-time notifications
- [ ] Add advanced filtering and search

### Q2 2024

- [ ] Mobile-responsive design improvements
- [ ] API integrations (Slack, Teams, email)
- [ ] Advanced reporting and analytics
- [ ] Multi-tenant support

### Q3 2024

- [ ] AI-powered control suggestions
- [ ] Automated evidence collection (optional)
- [ ] Advanced workflow automation
- [ ] Enterprise features

---

**🎉 Ready to get started?** Follow the [Setup Guide](SETUP_GUIDE.md) to deploy your modular compliance platform in minutes!
