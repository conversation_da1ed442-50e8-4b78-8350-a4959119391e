'use client'

import { Card } from '@/components/ui'
import { usePCIDSSCompliance } from '../hooks/usePCIDSS'
import { 
    PiShieldCheckDuotone, 
    PiWarningDuotone, 
    PiClockDuotone,
    PiChartBarDuotone 
} from 'react-icons/pi'

const PCIDSSOverview = () => {
    const { complianceSummary, overallMetrics, loading } = usePCIDSSCompliance()

    if (loading) {
        return (
            <div className="p-6">
                <div className="text-center">
                    <p>Loading PCI DSS compliance data...</p>
                </div>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl">
                    <PiShieldCheckDuotone className="text-2xl text-white" />
                </div>
                <div>
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                        PCI DSS 4.0.1 Compliance
                    </h2>
                    <p className="text-gray-600 dark:text-gray-400">
                        Payment Card Industry Data Security Standard
                    </p>
                </div>
            </div>

            {/* Overall Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <Card className="p-6">
                    <div className="flex items-center gap-4">
                        <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
                            <PiChartBarDuotone className="text-2xl text-green-600 dark:text-green-400" />
                        </div>
                        <div>
                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                                {overallMetrics.compliancePercentage}%
                            </h3>
                            <p className="text-gray-600 dark:text-gray-400">Overall Compliance</p>
                        </div>
                    </div>
                </Card>

                <Card className="p-6">
                    <div className="flex items-center gap-4">
                        <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                            <PiShieldCheckDuotone className="text-2xl text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                                {overallMetrics.compliantRequirements}/{overallMetrics.totalRequirements}
                            </h3>
                            <p className="text-gray-600 dark:text-gray-400">Compliant Requirements</p>
                        </div>
                    </div>
                </Card>

                <Card className="p-6">
                    <div className="flex items-center gap-4">
                        <div className="p-3 bg-red-100 dark:bg-red-900/20 rounded-lg">
                            <PiWarningDuotone className="text-2xl text-red-600 dark:text-red-400" />
                        </div>
                        <div>
                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                                {overallMetrics.criticalFindings + overallMetrics.highFindings}
                            </h3>
                            <p className="text-gray-600 dark:text-gray-400">Critical/High Findings</p>
                        </div>
                    </div>
                </Card>

                <Card className="p-6">
                    <div className="flex items-center gap-4">
                        <div className="p-3 bg-amber-100 dark:bg-amber-900/20 rounded-lg">
                            <PiClockDuotone className="text-2xl text-amber-600 dark:text-amber-400" />
                        </div>
                        <div>
                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                                {overallMetrics.upcomingDeadlines}
                            </h3>
                            <p className="text-gray-600 dark:text-gray-400">Upcoming Deadlines</p>
                        </div>
                    </div>
                </Card>
            </div>

            {/* Compliance by Category */}
            <Card className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Compliance by Category
                </h3>
                <div className="space-y-4">
                    {complianceSummary.map((category, index) => (
                        <div key={index} className="space-y-2">
                            <div className="flex items-center justify-between">
                                <h4 className="font-medium text-gray-900 dark:text-white">
                                    {category.category}
                                </h4>
                                <span className="text-sm text-gray-600 dark:text-gray-400">
                                    {category.compliant}/{category.total} ({category.percentage}%)
                                </span>
                            </div>
                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div 
                                    className={`h-2 rounded-full transition-all duration-300 ${
                                        category.percentage >= 80 
                                            ? 'bg-green-500' 
                                            : category.percentage >= 60 
                                                ? 'bg-yellow-500' 
                                                : 'bg-red-500'
                                    }`}
                                    style={{ width: `${category.percentage}%` }}
                                />
                            </div>
                        </div>
                    ))}
                </div>
            </Card>

            {/* Requirements Status Summary */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        Requirements Status
                    </h3>
                    <div className="space-y-3">
                        <div className="flex items-center justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Compliant</span>
                            <span className="font-semibold text-green-600 dark:text-green-400">
                                {overallMetrics.compliantRequirements}
                            </span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className="text-gray-600 dark:text-gray-400">In Progress</span>
                            <span className="font-semibold text-blue-600 dark:text-blue-400">
                                {overallMetrics.inProgressRequirements}
                            </span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Non-Compliant</span>
                            <span className="font-semibold text-red-600 dark:text-red-400">
                                {overallMetrics.nonCompliantRequirements}
                            </span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Not Started</span>
                            <span className="font-semibold text-gray-600 dark:text-gray-400">
                                {overallMetrics.notStartedRequirements}
                            </span>
                        </div>
                    </div>
                </Card>

                <Card className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        Findings by Risk Level
                    </h3>
                    <div className="space-y-3">
                        <div className="flex items-center justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Critical</span>
                            <span className="font-semibold text-red-600 dark:text-red-400">
                                {overallMetrics.criticalFindings}
                            </span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className="text-gray-600 dark:text-gray-400">High</span>
                            <span className="font-semibold text-orange-600 dark:text-orange-400">
                                {overallMetrics.highFindings}
                            </span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Medium</span>
                            <span className="font-semibold text-yellow-600 dark:text-yellow-400">
                                {overallMetrics.mediumFindings}
                            </span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Low</span>
                            <span className="font-semibold text-green-600 dark:text-green-400">
                                {overallMetrics.lowFindings}
                            </span>
                        </div>
                    </div>
                </Card>
            </div>
        </div>
    )
}

export default PCIDSSOverview
