# This workflow file defines automated checks that run on every Pull Request
# to ensure code quality, security, and functionality before merging changes.
#
# WORKFLOW SUMMARY:
# ================
# This CI/CD pipeline performs comprehensive validation of pull requests through 4 main jobs:
# 1. CODE QUALITY - Linting, formatting, type checking, dependency analysis, and bundle size
# 2. AUTOMATED TESTS - Unit tests, component tests, integration tests with coverage reporting
# 3. BUILD VERIFICATION - Ensures the application builds successfully and can start properly
# 4. SECURITY CHECKS - Vulnerability scanning and secret detection
# 5. PR SUMMARY - Generates a summary comment on the PR with all check results
#
# The workflow is triggered on PR events (open, sync, reopen) targeting develop or main branches.
# All jobs run in parallel except the summary job which waits for all others to complete.

# CI/CD Workflow for Pull Request Validation
# This workflow runs automated checks on every PR to ensure code quality, security, and functionality
name: Pull Request Checks

# Trigger: When PRs are opened, updated (synchronized), or reopened
on:
    pull_request:
        branches: [develop, main]  # Only run on PRs targeting develop or main branches
        types: [opened, synchronize, reopened]  # Run on PR creation, updates, and reopening

# Global environment variables used across all jobs
env:
    NODE_VERSION: '18'  # Node.js version to use consistently across all jobs

jobs:
    # ===== CODE QUALITY CHECKS =====
    # This job ensures code follows standards and best practices
    code-quality:
        name: Code Quality
        runs-on: ubuntu-latest  # Use latest Ubuntu runner for consistency

        steps:
            # Step 1: Get the source code from the repository
            - name: Checkout code
              uses: actions/checkout@v4  # Official GitHub action to checkout code
              with:
                  fetch-depth: 0  # Fetch full git history (needed for some tools)

            # Step 2: Set up Node.js environment
            - name: Setup Node.js
              uses: actions/setup-node@v4  # Official action to setup Node.js
              with:
                  node-version: ${{ env.NODE_VERSION }}  # Use Node.js 18 from env vars
                  cache: 'npm'  # Cache npm dependencies for faster builds

            # Step 3: Install project dependencies (clean install)
            - name: Install dependencies
              run: npm ci  # Clean install - faster and more reliable than npm install

            # Step 4: Check code style and potential issues with ESLint
            - name: Run ESLint
              run: npm run lint  # Runs ESLint to check for code quality issues

            # Step 5: Verify code formatting with Prettier
            - name: Run Prettier check
              run: npm run format:check  # Checks if code is properly formatted

            # Step 6: Verify TypeScript types are correct
            - name: Run TypeScript check
              run: npm run type-check  # Runs TypeScript compiler to check types

            # Step 7: Find unused dependencies to keep project clean
            - name: Check for unused dependencies
              run: npx depcheck  # Scans for unused dependencies in package.json

            # Step 8: Ensure bundle size hasn't grown too large
            - name: Check bundle size
              run: npm run build && npx bundlesize  # Build and check if bundle size is acceptable

    # ===== AUTOMATED TESTS =====
    # This job runs all types of tests to ensure functionality works correctly
    tests:
        name: Automated Tests
        runs-on: ubuntu-latest  # Use latest Ubuntu runner

        steps:
            # Step 1: Get the source code
            - name: Checkout code
              uses: actions/checkout@v4  # Checkout code (no full history needed for tests)

            # Step 2: Set up Node.js environment
            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: ${{ env.NODE_VERSION }}  # Use Node.js 18
                  cache: 'npm'  # Cache dependencies for faster execution

            # Step 3: Install all project dependencies
            - name: Install dependencies
              run: npm ci  # Clean install for consistent test environment

            # Step 4: Run unit tests with coverage reporting
            - name: Run unit tests
              run: npm run test:coverage  # Runs Jest tests and generates coverage report

            # Step 5: Upload test coverage to external service for tracking
            - name: Upload coverage reports
              uses: codecov/codecov-action@v3  # Third-party action for coverage reporting
              with:
                  file: ./coverage/lcov.info  # Coverage file generated by Jest
                  flags: unittests  # Tag this as unit test coverage
                  name: codecov-umbrella  # Name for this coverage upload

            # Step 6: Run component-specific tests (React components)
            - name: Run component tests
              run: npm run test:components  # Tests for individual React components

            # Step 7: Run integration tests (full feature testing)
            - name: Run integration tests
              run: npm run test:integration  # Tests that verify features work together

    # ===== BUILD VERIFICATION =====
    # This job ensures the application can be built and started successfully
    build:
        name: Build Verification
        runs-on: ubuntu-latest  # Use latest Ubuntu runner

        steps:
            # Step 1: Get the source code
            - name: Checkout code
              uses: actions/checkout@v4  # Checkout code for building

            # Step 2: Set up Node.js environment
            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: ${{ env.NODE_VERSION }}  # Use Node.js 18
                  cache: 'npm'  # Cache for faster builds

            # Step 3: Install dependencies needed for building
            - name: Install dependencies
              run: npm ci  # Clean install for consistent build environment

            # Step 4: Build the Next.js application for production
            - name: Build application
              run: npm run build  # Runs Next.js build process

            # Step 5: Verify the build created the expected output directory
            - name: Check build output
              run: |
                  # Check if Next.js created the .next directory (build output)
                  if [ ! -d ".next" ]; then
                    echo "❌ Build failed - .next directory not found"
                    exit 1  # Fail the job if build directory doesn't exist
                  fi
                  echo "✅ Build successful"

            # Step 6: Test that the built application can actually start and serve requests
            - name: Test build start
              run: |
                  npm run start &  # Start the production server in background
                  sleep 10  # Wait 10 seconds for server to fully start
                  curl -f http://localhost:3000 || exit 1  # Test if homepage loads (fail if not)
                  echo "✅ Application starts successfully"

    # ===== SECURITY CHECKS =====
    # This job scans for security vulnerabilities and exposed secrets
    security:
        name: Security Checks
        runs-on: ubuntu-latest  # Use latest Ubuntu runner

        steps:
            # Step 1: Get the source code
            - name: Checkout code
              uses: actions/checkout@v4  # Checkout code for security scanning

            # Step 2: Set up Node.js environment
            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: ${{ env.NODE_VERSION }}  # Use Node.js 18
                  cache: 'npm'  # Cache dependencies

            # Step 3: Install dependencies to check for vulnerabilities
            - name: Install dependencies
              run: npm ci  # Install packages to scan for known vulnerabilities

            # Step 4: Scan npm packages for known security vulnerabilities
            - name: Run security audit
              run: npm audit --audit-level=moderate  # Check for moderate+ severity vulnerabilities

            # Step 5: Scan code for accidentally committed secrets (API keys, passwords, etc.)
            - name: Check for secrets
              uses: trufflesecurity/trufflehog@main  # Third-party tool for secret detection
              with:
                  path: ./  # Scan entire repository
                  base: main  # Compare against main branch
                  head: HEAD  # Scan up to current commit

    # ===== PR SUMMARY =====
    # This job creates a summary comment on the PR with all check results
    pr-summary:
        name: PR Summary
        runs-on: ubuntu-latest  # Use latest Ubuntu runner
        needs: [code-quality, tests, build, security]  # Wait for all other jobs to complete
        if: always()  # Run even if some jobs failed (to show the summary)

        steps:
            # Step 1: Create a summary comment on the PR with all check results
            - name: Create PR Summary
              uses: actions/github-script@v7  # Official GitHub action for running JavaScript
              with:
                  script: |
                      // Collect results from all previous jobs
                      const results = {
                        'Code Quality': '${{ needs.code-quality.result }}',  // success/failure/cancelled
                        'Tests': '${{ needs.tests.result }}',
                        'Build': '${{ needs.build.result }}',
                        'Security': '${{ needs.security.result }}'
                      };

                      // Start building the markdown summary
                      let summary = '## 🔍 Pull Request Check Results\n\n';

                      // Add each check result with appropriate icon
                      for (const [check, result] of Object.entries(results)) {
                        const icon = result === 'success' ? '✅' : result === 'failure' ? '❌' : '⏸️';
                        summary += `${icon} **${check}**: ${result}\n`;
                      }

                      // Check if all jobs passed
                      const allPassed = Object.values(results).every(result => result === 'success');

                      // Add final message based on overall result
                      if (allPassed) {
                        summary += '\n🎉 **All checks passed!** This PR is ready for review.';
                      } else {
                        summary += '\n⚠️ **Some checks failed.** Please review and fix the issues above.';
                      }

                      // Post the summary as a comment on the PR
                      github.rest.issues.createComment({
                        issue_number: context.issue.number,  // PR number
                        owner: context.repo.owner,  // Repository owner
                        repo: context.repo.repo,  // Repository name
                        body: summary  // The markdown summary we built
                      });
