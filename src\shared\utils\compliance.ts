// Utility functions for compliance operations

import {
    Framework,
    Control,
    Task,
    Risk,
    ComplianceMetrics,
} from '../types/compliance'

/**
 * Calculate completion percentage for a framework
 */
export const calculateFrameworkProgress = (controls: Control[]): number => {
    if (controls.length === 0) return 0

    const compliantControls = controls.filter(
        (control) =>
            control.status === 'Compliant' || control.status === 'Implemented',
    ).length

    return Math.round((compliantControls / controls.length) * 100)
}

/**
 * Calculate risk score based on likelihood and impact
 */
export const calculateRiskScore = (
    likelihood: number,
    impact: number,
): number => {
    return likelihood * impact
}

/**
 * Get risk level based on risk score
 */
export const getRiskLevel = (
    riskScore: number,
): 'Low' | 'Medium' | 'High' | 'Critical' => {
    if (riskScore <= 4) return 'Low'
    if (riskScore <= 9) return 'Medium'
    if (riskScore <= 16) return 'High'
    return 'Critical'
}

/**
 * Check if a task is overdue
 */
export const isTaskOverdue = (task: Task): boolean => {
    if (task.status === 'Completed' || task.status === 'Cancelled') return false
    return new Date(task.dueDate) < new Date()
}

/**
 * Get overdue tasks from a list
 */
export const getOverdueTasks = (tasks: Task[]): Task[] => {
    return tasks.filter(isTaskOverdue)
}

/**
 * Calculate days until due date
 */
export const getDaysUntilDue = (dueDate: string): number => {
    const due = new Date(dueDate)
    const now = new Date()
    const diffTime = due.getTime() - now.getTime()
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

/**
 * Format due date with relative time
 */
export const formatDueDate = (dueDate: string): string => {
    const days = getDaysUntilDue(dueDate)

    if (days < 0) {
        return `${Math.abs(days)} days overdue`
    } else if (days === 0) {
        return 'Due today'
    } else if (days === 1) {
        return 'Due tomorrow'
    } else if (days <= 7) {
        return `Due in ${days} days`
    } else {
        return new Date(dueDate).toLocaleDateString()
    }
}

/**
 * Generate next occurrence for recurring tasks
 */
export const getNextOccurrence = (
    lastDate: string,
    frequency: string,
    interval: number = 1,
): string => {
    const last = new Date(lastDate)
    const next = new Date(last)

    switch (frequency) {
        case 'Daily':
            next.setDate(last.getDate() + interval)
            break
        case 'Weekly':
            next.setDate(last.getDate() + 7 * interval)
            break
        case 'Monthly':
            next.setMonth(last.getMonth() + interval)
            break
        case 'Quarterly':
            next.setMonth(last.getMonth() + 3 * interval)
            break
        case 'Annually':
            next.setFullYear(last.getFullYear() + interval)
            break
        default:
            return lastDate
    }

    return next.toISOString().split('T')[0]
}

/**
 * Filter controls by framework
 */
export const getControlsByFramework = (
    controls: Control[],
    frameworkId: string,
): Control[] => {
    return controls.filter((control) => control.frameworkId === frameworkId)
}

/**
 * Get controls by status
 */
export const getControlsByStatus = (
    controls: Control[],
    status: string,
): Control[] => {
    return controls.filter((control) => control.status === status)
}

/**
 * Get high-risk items (controls, tasks, risks)
 */
export const getHighRiskItems = (controls: Control[], risks: Risk[]) => {
    const highRiskControls = controls.filter(
        (control) =>
            control.riskLevel === 'High' || control.riskLevel === 'Critical',
    )

    const highRisks = risks.filter(
        (risk) => risk.riskScore >= 12, // High and Critical risks
    )

    return {
        controls: highRiskControls,
        risks: highRisks,
        total: highRiskControls.length + highRisks.length,
    }
}

/**
 * Calculate compliance metrics for dashboard
 */
export const calculateComplianceMetrics = (
    frameworks: Framework[],
    controls: Control[],
    tasks: Task[],
    risks: Risk[],
): ComplianceMetrics => {
    const activeFrameworks = frameworks.filter((f) => f.status === 'Active')
    const compliantControls = controls.filter(
        (c) => c.status === 'Compliant' || c.status === 'Implemented',
    )
    const overdueTasks = getOverdueTasks(tasks)
    const highRiskItems = getHighRiskItems(controls, risks)

    const completionRate =
        controls.length > 0
            ? Math.round((compliantControls.length / controls.length) * 100)
            : 0

    // Generate mock trends data - in real implementation, this would come from historical data
    const trendsData = Array.from({ length: 30 }, (_, i) => {
        const date = new Date()
        date.setDate(date.getDate() - (29 - i))

        return {
            date: date.toISOString().split('T')[0],
            completionRate: Math.max(0, completionRate - Math.random() * 10),
            newTasks: Math.floor(Math.random() * 5),
            completedTasks: Math.floor(Math.random() * 8),
        }
    })

    return {
        totalFrameworks: frameworks.length,
        activeFrameworks: activeFrameworks.length,
        totalControls: controls.length,
        compliantControls: compliantControls.length,
        overdueTasks: overdueTasks.length,
        highRisks: highRiskItems.total,
        completionRate,
        trendsData,
    }
}

/**
 * Export data to CSV format
 */
export const exportToCSV = (
    data: Record<string, unknown>[],
    filename: string,
) => {
    if (data.length === 0) return

    const headers = Object.keys(data[0])
    const csvContent = [
        headers.join(','),
        ...data.map((row) =>
            headers
                .map((header) => {
                    const value = row[header]
                    // Escape commas and quotes in CSV
                    if (
                        typeof value === 'string' &&
                        (value.includes(',') || value.includes('"'))
                    ) {
                        return `"${value.replace(/"/g, '""')}"`
                    }
                    return value
                })
                .join(','),
        ),
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')

    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', `${filename}.csv`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    }
}

/**
 * Generate unique ID
 */
export const generateId = (): string => {
    return Math.random().toString(36).substr(2, 9)
}

/**
 * Debounce function for search inputs
 */
export const debounce = <T extends (...args: unknown[]) => unknown>(
    func: T,
    wait: number,
): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout

    return (...args: Parameters<T>) => {
        clearTimeout(timeout)
        timeout = setTimeout(() => func(...args), wait)
    }
}
