import {
    PiSquaresFourDuotone,
    PiProjectorScreenChartDuotone,
    PiUserCircleDuotone,
    PiSparkleDuotone,
    PiQuestionDuotone,
    PiShieldCheckDuotone,
    PiShoppingCartSimpleDuotone,
    PiMegaphoneDuotone,
    PiAppWindowDuotone,
} from 'react-icons/pi'
import type { IconConfig } from './types'

const demoCategoriesIcons: IconConfig = {
    all: <PiSquaresFourDuotone />,
    ecommerce: <PiShoppingCartSimpleDuotone />,
    project: <PiProjectorScreenChartDuotone />,
    marketing: <PiMegaphoneDuotone />,
    ai: <PiSparkleDuotone />,
    helpCenter: <PiQuestionDuotone />,
    apps: <PiAppWindowDuotone />,
    accounts: <PiUserCircleDuotone />,
    auth: <PiShieldCheckDuotone />,
}

export default demoCategoriesIcons
