'use client'

import { createClient } from '@/lib/supabase/client'
import { useCallback, useEffect, useState } from 'react'
import { FilterOptions, PaginationOptions } from '../types/compliance'

export const useSupabase = () => {
    const supabase = createClient()

    return supabase
}

export const useSupabaseQuery = <T>(
    table: string,
    filters?: FilterOptions,
    pagination?: PaginationOptions,
) => {
    const [data, setData] = useState<T[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [total, setTotal] = useState(0)

    const supabase = useSupabase()

    const fetchData = useCallback(async () => {
        try {
            setLoading(true)
            setError(null)

            let query = supabase.from(table).select('*', { count: 'exact' })

            // Apply filters
            if (filters?.search) {
                // This is a simple implementation - you might want to customize based on table structure
                query = query.or(
                    `title.ilike.%${filters.search}%,name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`,
                )
            }

            if (filters?.status && filters.status.length > 0) {
                query = query.in('status', filters.status)
            }

            if (filters?.category && filters.category.length > 0) {
                query = query.in('category', filters.category)
            }

            if (filters?.owner && filters.owner.length > 0) {
                query = query.in('owner', filters.owner)
            }

            if (filters?.assignee && filters.assignee.length > 0) {
                query = query.in('assignee', filters.assignee)
            }

            if (filters?.tags && filters.tags.length > 0) {
                query = query.overlaps('tags', filters.tags)
            }

            if (filters?.dateRange) {
                query = query
                    .gte('created_at', filters.dateRange.start)
                    .lte('created_at', filters.dateRange.end)
            }

            // Apply pagination and sorting
            if (pagination) {
                const { page, limit, sortBy, sortOrder } = pagination
                const from = (page - 1) * limit
                const to = from + limit - 1

                query = query.range(from, to)

                if (sortBy) {
                    query = query.order(sortBy, {
                        ascending: sortOrder === 'asc',
                    })
                }
            }

            const { data: result, error: queryError, count } = await query

            if (queryError) {
                throw queryError
            }

            setData(result || [])
            setTotal(count || 0)
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An error occurred')
        } finally {
            setLoading(false)
        }
    }, [table, filters, pagination, supabase])

    useEffect(() => {
        fetchData()
    }, [fetchData])

    return {
        data,
        loading,
        error,
        total,
        refetch: fetchData,
    }
}

export const useSupabaseMutation = <T>(table: string) => {
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)

    const supabase = useSupabase()

    const create = useCallback(
        async (data: Partial<T>): Promise<T | null> => {
            try {
                setLoading(true)
                setError(null)

                const { data: result, error: createError } = await supabase
                    .from(table)
                    .insert(data)
                    .select()
                    .single()

                if (createError) {
                    throw createError
                }

                return result
            } catch (err) {
                setError(
                    err instanceof Error ? err.message : 'An error occurred',
                )
                return null
            } finally {
                setLoading(false)
            }
        },
        [table, supabase],
    )

    const update = useCallback(
        async (id: string, data: Partial<T>): Promise<T | null> => {
            try {
                setLoading(true)
                setError(null)

                const { data: result, error: updateError } = await supabase
                    .from(table)
                    .update(data)
                    .eq('id', id)
                    .select()
                    .single()

                if (updateError) {
                    throw updateError
                }

                return result
            } catch (err) {
                setError(
                    err instanceof Error ? err.message : 'An error occurred',
                )
                return null
            } finally {
                setLoading(false)
            }
        },
        [table, supabase],
    )

    const remove = useCallback(
        async (id: string): Promise<boolean> => {
            try {
                setLoading(true)
                setError(null)

                const { error: deleteError } = await supabase
                    .from(table)
                    .delete()
                    .eq('id', id)

                if (deleteError) {
                    throw deleteError
                }

                return true
            } catch (err) {
                setError(
                    err instanceof Error ? err.message : 'An error occurred',
                )
                return false
            } finally {
                setLoading(false)
            }
        },
        [table, supabase],
    )

    return {
        create,
        update,
        remove,
        loading,
        error,
    }
}

// Specialized hooks for common operations
export const useFrameworks = (
    filters?: FilterOptions,
    pagination?: PaginationOptions,
) => {
    return useSupabaseQuery('frameworks', filters, pagination)
}

export const useControls = (
    filters?: FilterOptions,
    pagination?: PaginationOptions,
) => {
    return useSupabaseQuery('controls', filters, pagination)
}

export const useTasks = (
    filters?: FilterOptions,
    pagination?: PaginationOptions,
) => {
    return useSupabaseQuery('tasks', filters, pagination)
}

export const useRisks = (
    filters?: FilterOptions,
    pagination?: PaginationOptions,
) => {
    return useSupabaseQuery('risks', filters, pagination)
}

export const useVendors = (
    filters?: FilterOptions,
    pagination?: PaginationOptions,
) => {
    return useSupabaseQuery('vendors', filters, pagination)
}

export const usePolicies = (
    filters?: FilterOptions,
    pagination?: PaginationOptions,
) => {
    return useSupabaseQuery('policies', filters, pagination)
}

export const useAssets = (
    filters?: FilterOptions,
    pagination?: PaginationOptions,
) => {
    return useSupabaseQuery('assets', filters, pagination)
}

export const useEvidence = (
    filters?: FilterOptions,
    pagination?: PaginationOptions,
) => {
    return useSupabaseQuery('evidence', filters, pagination)
}
