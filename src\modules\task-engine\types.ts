// Import types first
import type {
    Task,
    TaskComment,
    RecurrenceRule,
    Framework,
    Control,
    FilterOptions,
    PaginationOptions,
} from '@/shared/types/compliance'

// Task Engine module types
export interface TaskDetail extends Task {
    framework?: Framework
    controls: Control[]
    assigneeDetails?: {
        id: string
        name: string
        email: string
        avatar?: string
    }
    ownerDetails?: {
        id: string
        name: string
        email: string
        avatar?: string
    }
    subtasks?: Task[]
    dependencies?: Task[]
    timeTracking?: {
        started?: string
        paused?: string
        totalTime: number
        sessions: TimeSession[]
    }
}

export interface TimeSession {
    id: string
    startTime: string
    endTime?: string
    duration: number
    description?: string
}

export interface TaskTemplate {
    id: string
    title: string
    description?: string
    type: 'One-time' | 'Recurring'
    frequency?: 'Daily' | 'Weekly' | 'Monthly' | 'Quarterly' | 'Annually'
    estimatedHours?: number
    priority: 'Low' | 'Medium' | 'High' | 'Critical'
    category: string
    tags: string[]
    controlIds: string[]
    frameworkIds: string[]
    instructions?: string
    checklistItems?: ChecklistItem[]
}

export interface ChecklistItem {
    id: string
    text: string
    completed: boolean
    required: boolean
}

export interface TaskNotification {
    id: string
    taskId: string
    type: 'Due Soon' | 'Overdue' | 'Assigned' | 'Completed' | 'Comment'
    message: string
    read: boolean
    createdAt: string
    userId: string
}

export interface TaskFilter extends FilterOptions {
    type?: string[]
    dueDate?: {
        start?: string
        end?: string
    }
    overdue?: boolean
    assignedToMe?: boolean
    createdByMe?: boolean
    frameworkId?: string
    controlId?: string
}

// Re-export shared types for convenience
export type {
    Task,
    TaskComment,
    RecurrenceRule,
    Framework,
    Control,
    FilterOptions,
    PaginationOptions,
}
