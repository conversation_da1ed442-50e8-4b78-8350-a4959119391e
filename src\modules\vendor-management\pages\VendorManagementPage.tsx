'use client'

import { VendorCard } from '../components'
import { useMockVendors, useVendorExport } from '../hooks'
import { Vendor } from '@/shared/types/compliance'
import { FilterBar } from '@/shared/components'
import {
    VENDOR_CATEGORIES,
    VENDOR_STATUSES,
    RISK_LEVELS,
} from '@/shared/constants/compliance'
import Button from '@/components/ui/Button'
import { HiPlus, HiOutlineDownload } from 'react-icons/hi'

const VendorManagementPage = () => {
    const { data: vendors, loading, error } = useMockVendors()
    const { exportVendorsToCSV } = useVendorExport()

    const filterOptions = {
        status: VENDOR_STATUSES.map((status) => ({
            value: status,
            label: status,
        })),
        category: VENDOR_CATEGORIES.map((category) => ({
            value: category,
            label: category,
        })),
        priority: RISK_LEVELS.map((level) => ({ value: level, label: level })),
    }

    const handleViewVendor = (vendor: Vendor) => {
        console.log('View vendor:', vendor)
        // TODO: Navigate to vendor detail page or open modal
    }

    const handleEditVendor = (vendor: Vendor) => {
        console.log('Edit vendor:', vendor)
        // TODO: Open edit modal or navigate to edit page
    }

    const handleViewDocuments = (vendor: Vendor) => {
        console.log('View documents for vendor:', vendor)
        // TODO: Navigate to documents page or open modal
    }

    const handleViewControls = (vendor: Vendor) => {
        console.log('View controls for vendor:', vendor)
        // TODO: Navigate to control hub with vendor filter
    }

    const handleAddVendor = () => {
        console.log('Add new vendor')
        // TODO: Open add vendor modal or navigate to add page
    }

    const handleExportCSV = () => {
        exportVendorsToCSV(vendors)
    }

    if (error) {
        return (
            <div className="p-6">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h3 className="text-red-800 font-medium">
                        Error loading vendors
                    </h3>
                    <p className="text-red-600 text-sm mt-1">{error}</p>
                </div>
            </div>
        )
    }

    return (
        <div className="p-6 space-y-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 className="text-2xl font-bold">Vendor Management</h1>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">
                        Manage third-party vendors and service providers
                    </p>
                </div>
                <div className="flex gap-2">
                    <Button
                        variant="default"
                        icon={<HiOutlineDownload />}
                        onClick={handleExportCSV}
                    >
                        Export CSV
                    </Button>
                    <Button
                        variant="solid"
                        icon={<HiPlus />}
                        onClick={handleAddVendor}
                        className="bg-emerald-500 hover:bg-emerald-600"
                    >
                        Add Vendor
                    </Button>
                </div>
            </div>

            {/* Filters */}
            <FilterBar
                searchPlaceholder="Search vendors..."
                filters={filterOptions}
                onFilterChange={() => {}}
            />

            {/* Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="text-2xl font-bold text-blue-600">
                        {vendors.length}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        Total Vendors
                    </div>
                </div>
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="text-2xl font-bold text-emerald-600">
                        {vendors.filter((v) => v.status === 'Active').length}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        Active
                    </div>
                </div>
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="text-2xl font-bold text-red-600">
                        {
                            vendors.filter(
                                (v) =>
                                    v.riskLevel === 'High' ||
                                    v.riskLevel === 'Critical',
                            ).length
                        }
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        High Risk
                    </div>
                </div>
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="text-2xl font-bold text-amber-600">
                        {
                            vendors.filter((v) => {
                                if (!v.contractEndDate) return false
                                const endDate = new Date(v.contractEndDate)
                                const today = new Date()
                                const daysUntilExpiry = Math.ceil(
                                    (endDate.getTime() - today.getTime()) /
                                        (1000 * 60 * 60 * 24),
                                )
                                return (
                                    daysUntilExpiry <= 90 && daysUntilExpiry > 0
                                )
                            }).length
                        }
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        Expiring Soon
                    </div>
                </div>
            </div>

            {/* Vendor Grid */}
            {loading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {Array.from({ length: 6 }).map((_, i) => (
                        <div
                            key={i}
                            className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"
                        ></div>
                    ))}
                </div>
            ) : vendors.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {vendors.map((vendor) => (
                        <VendorCard
                            key={vendor.id}
                            vendor={vendor}
                            onView={handleViewVendor}
                            onEdit={handleEditVendor}
                            onViewDocuments={handleViewDocuments}
                            onViewControls={handleViewControls}
                        />
                    ))}
                </div>
            ) : (
                <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                        <svg
                            className="w-16 h-16 mx-auto"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={1}
                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                            />
                        </svg>
                    </div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                        No vendors found
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                        Get started by adding your first vendor or service
                        provider.
                    </p>
                    <Button
                        variant="solid"
                        icon={<HiPlus />}
                        onClick={handleAddVendor}
                        className="bg-emerald-500 hover:bg-emerald-600"
                    >
                        Add Vendor
                    </Button>
                </div>
            )}
        </div>
    )
}

export default VendorManagementPage
