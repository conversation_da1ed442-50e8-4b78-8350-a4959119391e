name: CI/CD Pipeline

on:
    push:
        branches: [develop, main]
    pull_request:
        branches: [develop, main]

env:
    NODE_VERSION: '18'

jobs:
    # ===== TESTING JOBS =====
    test:
        name: Run Tests
        runs-on: ubuntu-latest
        if: github.event_name == 'pull_request' || github.ref == 'refs/heads/develop'

        steps:
            - name: Checkout code
              uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: ${{ env.NODE_VERSION }}
                  cache: 'npm'

            - name: Install dependencies
              run: npm ci

            - name: Run linting
              run: npm run lint

            - name: Run type checking
              run: npm run type-check

            - name: Run unit tests
              run: npm run test

            - name: Build application
              run: npm run build

            - name: Upload build artifacts
              uses: actions/upload-artifact@v4
              with:
                  name: build-files
                  path: .next/
                  retention-days: 1

    # ===== SECURITY SCAN =====
    security:
        name: Security Scan
        runs-on: ubuntu-latest
        if: github.event_name == 'pull_request' || github.ref == 'refs/heads/develop'

        steps:
            - name: Checkout code
              uses: actions/checkout@v4

            - name: Run security audit
              run: npm audit --audit-level=high

            - name: Check for vulnerabilities
              run: npm audit --audit-level=moderate

    # ===== DEVELOPMENT VALIDATION =====
    # This job runs on develop branch to validate changes before merging to main
    # No deployment occurs - only validation and testing
    validate-develop:
        name: Validate Development Branch
        runs-on: ubuntu-latest
        needs: [test, security]
        if: github.ref == 'refs/heads/develop' && github.event_name == 'push'

        steps:
            - name: Development Branch Validation Complete
              run: |
                  echo "✅ All checks passed on develop branch"
                  echo "🔄 Ready for merge to main branch"
                  echo "📋 Changes validated: tests, security, build, and code quality"

    # ===== PRODUCTION DEPLOYMENT =====
    deploy-production:
        name: Deploy to Production
        runs-on: ubuntu-latest
        needs: [test, security]
        if: github.ref == 'refs/heads/main' && github.event_name == 'push'
        environment: production

        steps:
            - name: Checkout code
              uses: actions/checkout@v4

            - name: Deploy to Vercel (Production)
              run: |
                  curl -X POST "${{ secrets.VERCEL_PRODUCTION_DEPLOY_HOOK }}"
                  echo "✅ Production deployment triggered via deploy hook"
                  echo "🔗 Production URL: https://checkgap.com"

            - name: Create Release Tag
              if: success()
              run: |
                  git config --local user.email "<EMAIL>"
                  git config --local user.name "GitHub Action"
                  git tag -a "v$(date +'%Y.%m.%d')-$(git rev-parse --short HEAD)" -m "Production release $(date +'%Y-%m-%d %H:%M:%S')"
                  git push origin --tags

    # ===== NOTIFICATION =====
    notify:
        name: Send Notifications
        runs-on: ubuntu-latest
        needs: [validate-develop, deploy-production]
        if: always()

        steps:
            - name: Notify Development Validation Success
              if: needs.validate-develop.result == 'success'
              run: |
                  echo "✅ Development branch validation successful!"
                  echo "🔄 Ready for production deployment"

            - name: Notify Production Deployment Success
              if: needs.deploy-production.result == 'success'
              run: |
                  echo "✅ Production deployment successful!"
                  echo "🌐 Live at: https://checkgap.com"

            - name: Notify Failure
              if: needs.validate-develop.result == 'failure' || needs.deploy-production.result == 'failure'
              run: |
                  echo "❌ Workflow failed - check logs for details"
