// Core compliance types for the modular platform

export interface Framework {
    id: string
    name: string
    version?: string
    category:
        | 'Cybersecurity'
        | 'Privacy'
        | 'Financial'
        | 'Healthcare'
        | 'Environmental'
        | 'Quality'
        | 'Strategic'
        | 'AI Ethics'
    description: string
    region: 'Global' | 'US' | 'EU' | 'Internal'
    status: 'Active' | 'In Progress' | 'Not Started' | 'Archived'
    controlCount: number
    progress: number
    lastUpdated: string
    notifications: number
    createdAt: string
    updatedAt: string
    createdBy: string
    organizationId: string
}

export interface Control {
    id: string
    frameworkId: string
    controlNumber: string
    title: string
    description: string
    category: string
    subcategory?: string
    requirements: string[]
    status:
        | 'Not Started'
        | 'In Progress'
        | 'Implemented'
        | 'Needs Review'
        | 'Compliant'
        | 'Non-Compliant'
    evidenceReadiness: 'Not Ready' | 'Partial' | 'Ready' | 'Approved'
    owner?: string
    assignee?: string
    dueDate?: string
    lastReviewDate?: string
    nextReviewDate?: string
    notes?: string
    tags: string[]
    riskLevel: 'Low' | 'Medium' | 'High' | 'Critical'
    implementationEffort: 'Low' | 'Medium' | 'High'
    businessImpact: 'Low' | 'Medium' | 'High'
    createdAt: string
    updatedAt: string
    organizationId: string
}

export interface Task {
    id: string
    title: string
    description?: string
    type: 'One-time' | 'Recurring'
    frequency?: 'Daily' | 'Weekly' | 'Monthly' | 'Quarterly' | 'Annually'
    status: 'Open' | 'In Progress' | 'Completed' | 'Overdue' | 'Cancelled'
    priority: 'Low' | 'Medium' | 'High' | 'Critical'
    assignee?: string
    owner?: string
    dueDate: string
    completedDate?: string
    estimatedHours?: number
    actualHours?: number
    controlIds: string[]
    frameworkIds: string[]
    tags: string[]
    attachments: string[]
    comments: TaskComment[]
    recurrenceRule?: RecurrenceRule
    parentTaskId?: string
    createdAt: string
    updatedAt: string
    organizationId: string
}

export interface TaskComment {
    id: string
    taskId: string
    content: string
    author: string
    createdAt: string
}

export interface RecurrenceRule {
    frequency: 'Daily' | 'Weekly' | 'Monthly' | 'Quarterly' | 'Annually'
    interval: number
    endDate?: string
    occurrences?: number
}

export interface Risk {
    id: string
    title: string
    description: string
    category:
        | 'Operational'
        | 'Financial'
        | 'Strategic'
        | 'Compliance'
        | 'Reputational'
        | 'Technology'
    likelihood: 1 | 2 | 3 | 4 | 5
    impact: 1 | 2 | 3 | 4 | 5
    riskScore: number
    inherentRisk: number
    residualRisk: number
    status:
        | 'Identified'
        | 'Assessed'
        | 'Mitigated'
        | 'Accepted'
        | 'Transferred'
        | 'Avoided'
    owner?: string
    controlIds: string[]
    mitigationPlan?: string
    mitigationDeadline?: string
    lastReviewDate?: string
    nextReviewDate?: string
    tags: string[]
    createdAt: string
    updatedAt: string
    organizationId: string
}

export interface Vendor {
    id: string
    name: string
    contactEmail?: string
    contactPhone?: string
    website?: string
    description?: string
    category:
        | 'Technology'
        | 'Professional Services'
        | 'Infrastructure'
        | 'Financial'
        | 'Other'
    riskLevel: 'Low' | 'Medium' | 'High' | 'Critical'
    status: 'Active' | 'Inactive' | 'Under Review' | 'Terminated'
    contractStartDate?: string
    contractEndDate?: string
    lastAssessmentDate?: string
    nextAssessmentDate?: string
    controlIds: string[]
    documents: VendorDocument[]
    tags: string[]
    createdAt: string
    updatedAt: string
    organizationId: string
}

export interface VendorDocument {
    id: string
    vendorId: string
    name: string
    type:
        | 'Contract'
        | 'SOC 2'
        | 'ISO 27001'
        | 'Security Assessment'
        | 'Insurance'
        | 'Other'
    url?: string
    expiryDate?: string
    status: 'Current' | 'Expired' | 'Pending Review'
    uploadedAt: string
    uploadedBy: string
}

export interface Policy {
    id: string
    title: string
    description?: string
    content: string
    version: string
    status: 'Draft' | 'Under Review' | 'Approved' | 'Published' | 'Archived'
    category: string
    owner?: string
    approver?: string
    effectiveDate?: string
    reviewDate?: string
    nextReviewDate?: string
    controlIds: string[]
    acknowledgments: PolicyAcknowledgment[]
    tags: string[]
    createdAt: string
    updatedAt: string
    organizationId: string
}

export interface PolicyAcknowledgment {
    id: string
    policyId: string
    userId: string
    acknowledgedAt: string
    version: string
}

export interface Asset {
    id: string
    name: string
    description?: string
    type:
        | 'System'
        | 'Application'
        | 'Database'
        | 'Network'
        | 'Physical'
        | 'Cloud Service'
        | 'Other'
    classification: 'Public' | 'Internal' | 'Confidential' | 'Restricted'
    owner?: string
    custodian?: string
    location?: string
    status: 'Active' | 'Inactive' | 'Decommissioned' | 'Under Review'
    lastReviewDate?: string
    nextReviewDate?: string
    controlIds: string[]
    riskIds: string[]
    tags: string[]
    createdAt: string
    updatedAt: string
    organizationId: string
}

export interface Evidence {
    id: string
    title: string
    description?: string
    type:
        | 'Screenshot'
        | 'Document'
        | 'Log File'
        | 'Certificate'
        | 'Report'
        | 'Other'
    url?: string
    fileName?: string
    fileSize?: number
    mimeType?: string
    status: 'Pending' | 'Approved' | 'Rejected' | 'Expired'
    controlIds: string[]
    taskIds: string[]
    vendorIds: string[]
    collectedDate: string
    expiryDate?: string
    reviewedBy?: string
    reviewedAt?: string
    tags: string[]
    createdAt: string
    updatedAt: string
    organizationId: string
}

// Common filter and pagination types
export interface FilterOptions {
    search?: string
    status?: string[]
    category?: string[]
    owner?: string[]
    assignee?: string[]
    tags?: string[]
    priority?: string[]
    type?: string[]
    dateRange?: {
        start: string
        end: string
    }
}

export interface PaginationOptions {
    page: number
    limit: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
}

export interface ApiResponse<T> {
    data: T
    meta?: {
        total: number
        page: number
        limit: number
        totalPages: number
    }
    error?: string
}

// Dashboard and analytics types
export interface ComplianceMetrics {
    totalFrameworks: number
    activeFrameworks: number
    totalControls: number
    compliantControls: number
    overdueTasks: number
    highRisks: number
    completionRate: number
    trendsData: {
        date: string
        completionRate: number
        newTasks: number
        completedTasks: number
    }[]
}

export interface ModuleConfig {
    id: string
    name: string
    description: string
    icon: string
    path: string
    enabled: boolean
    permissions: string[]
}
