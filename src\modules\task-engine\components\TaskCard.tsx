'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Avatar from '@/components/ui/Avatar'
import { StatusBadge } from '@/shared/components'
import { TaskDetail } from '../types'
import { formatDueDate, isTaskOverdue } from '@/shared/utils/compliance'
import {
    HiOutlineClock,
    HiOutlineUser,
    HiOutlineChat,
    HiOutlineClipboardCheck,
    HiOutlineExclamationCircle,
    HiOutlineRefresh,
    HiOutlinePlay,
    HiOutlineCheck,
    HiOutlineCog,
} from 'react-icons/hi'

interface TaskCardProps {
    task: TaskDetail
    onView?: (task: TaskDetail) => void
    onEdit?: (task: TaskDetail) => void
    onStart?: (task: TaskDetail) => void
    onComplete?: (task: TaskDetail) => void
    onComment?: (task: TaskDetail) => void
    compact?: boolean
}

const TaskCard = ({
    task,
    onView,
    onEdit,
    onStart,
    onComplete,
    onComment,
    compact = false,
}: TaskCardProps) => {
    const [isHovered, setIsHovered] = useState(false)

    const isOverdue = isTaskOverdue(task)
    const dueText = formatDueDate(task.dueDate)

    const getTypeIcon = (type: string) => {
        return type === 'Recurring' ? (
            <HiOutlineRefresh className="w-4 h-4" />
        ) : (
            <HiOutlineClipboardCheck className="w-4 h-4" />
        )
    }

    if (compact) {
        return (
            <div
                className={`flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-colors ${
                    isOverdue
                        ? 'border-red-200 bg-red-50 dark:bg-red-900/10'
                        : 'border-gray-200 dark:border-gray-700'
                }`}
                onClick={() => onView?.(task)}
            >
                <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                        {getTypeIcon(task.type)}
                        {isOverdue && (
                            <HiOutlineExclamationCircle className="w-4 h-4 text-red-500" />
                        )}
                    </div>

                    <div className="flex-1 min-w-0">
                        <h4 className="font-medium truncate">{task.title}</h4>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                            <span>{dueText}</span>
                            {task.assigneeDetails && (
                                <>
                                    <span>•</span>
                                    <span>{task.assigneeDetails.name}</span>
                                </>
                            )}
                        </div>
                    </div>
                </div>

                <div className="flex items-center gap-2">
                    <StatusBadge status={task.status} size="sm" />
                    <StatusBadge status={task.priority} size="sm" />
                </div>
            </div>
        )
    }

    return (
        <Card
            clickable
            className="h-full transition-all duration-200 hover:shadow-lg"
            bodyClass="h-full flex flex-col p-4"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={() => onView?.(task)}
        >
            {/* Header */}
            <div className="flex justify-between items-start mb-3">
                <div className="flex items-center gap-2">
                    {getTypeIcon(task.type)}
                    <StatusBadge status={task.status} size="sm" />
                    {isOverdue && (
                        <HiOutlineExclamationCircle
                            className="w-4 h-4 text-red-500"
                            title="Overdue"
                        />
                    )}
                </div>
                <StatusBadge status={task.priority} size="sm" />
            </div>

            {/* Task Info */}
            <div className="flex-1">
                <h4 className="font-semibold mb-2 line-clamp-2">
                    {task.title}
                </h4>

                {task.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                        {task.description}
                    </p>
                )}

                {/* Meta Info */}
                <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2 text-gray-500">
                        <HiOutlineClock className="w-4 h-4" />
                        <span
                            className={
                                isOverdue ? 'text-red-600 font-medium' : ''
                            }
                        >
                            {dueText}
                        </span>
                    </div>

                    {task.assigneeDetails && (
                        <div className="flex items-center gap-2">
                            <HiOutlineUser className="w-4 h-4 text-gray-500" />
                            <Avatar
                                size="sm"
                                src={task.assigneeDetails.avatar}
                            />
                            <span className="text-gray-600 dark:text-gray-400">
                                {task.assigneeDetails.name}
                            </span>
                        </div>
                    )}

                    {task.estimatedHours && (
                        <div className="text-gray-500">
                            Est. {task.estimatedHours}h
                            {task.actualHours &&
                                ` • Actual: ${task.actualHours}h`}
                        </div>
                    )}
                </div>

                {/* Tags */}
                {task.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-3">
                        {task.tags.slice(0, 3).map((tag) => (
                            <span
                                key={tag}
                                className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs rounded-md"
                            >
                                {tag}
                            </span>
                        ))}
                        {task.tags.length > 3 && (
                            <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs rounded-md">
                                +{task.tags.length - 3}
                            </span>
                        )}
                    </div>
                )}
            </div>

            {/* Footer */}
            <div className="flex justify-between items-center pt-3 border-t border-gray-200 dark:border-gray-700 mt-3">
                <div className="flex items-center gap-2 text-xs text-gray-500">
                    {task.comments.length > 0 && (
                        <div className="flex items-center gap-1">
                            <HiOutlineChat className="w-3 h-3" />
                            <span>{task.comments.length}</span>
                        </div>
                    )}
                    {task.controls.length > 0 && (
                        <div className="flex items-center gap-1">
                            <HiOutlineClipboardCheck className="w-3 h-3" />
                            <span>{task.controls.length} controls</span>
                        </div>
                    )}
                </div>

                {/* Action Buttons */}
                <div
                    className={`flex gap-1 transition-opacity duration-200 ${isHovered ? 'opacity-100' : 'opacity-0'}`}
                >
                    {task.status === 'Open' && (
                        <Button
                            size="xs"
                            variant="plain"
                            icon={<HiOutlinePlay />}
                            onClick={(e) => {
                                e.stopPropagation()
                                onStart?.(task)
                            }}
                            title="Start Task"
                        />
                    )}

                    {task.status === 'In Progress' && (
                        <Button
                            size="xs"
                            variant="plain"
                            icon={<HiOutlineCheck />}
                            onClick={(e) => {
                                e.stopPropagation()
                                onComplete?.(task)
                            }}
                            title="Complete Task"
                        />
                    )}

                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineChat />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onComment?.(task)
                        }}
                        title="Add Comment"
                    />
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineCog />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onEdit?.(task)
                        }}
                        title="Edit Task"
                    />
                </div>
            </div>
        </Card>
    )
}

export default TaskCard
