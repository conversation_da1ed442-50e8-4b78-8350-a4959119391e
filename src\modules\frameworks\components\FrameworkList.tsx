'use client'

import { useState, useMemo } from 'react'
import { FilterBar } from '@/shared/components'
import { FilterOptions } from '@/shared/types/compliance'
import {
    FRAMEWORK_CATEGORIES,
    FRAMEWORK_STATUSES,
} from '@/shared/constants/compliance'
import FrameworkCard from './FrameworkCard'
import { Framework } from '../types'
import Button from '@/components/ui/Button'
import { HiPlus } from 'react-icons/hi'

interface FrameworkListProps {
    frameworks: Framework[]
    loading?: boolean
    onView?: (framework: Framework) => void
    onEdit?: (framework: Framework) => void
    onManageControls?: (framework: Framework) => void
    onViewReports?: (framework: Framework) => void
    onAddFramework?: () => void
}

const FrameworkList = ({
    frameworks,
    loading = false,
    onView,
    onEdit,
    onManageControls,
    onViewReports,
    onAddFramework,
}: FrameworkListProps) => {
    const [filters, setFilters] = useState<FilterOptions>({})

    // Filter options for the FilterBar
    const filterOptions = {
        status: FRAMEWORK_STATUSES.map((status) => ({
            value: status,
            label: status,
        })),
        category: FRAMEWORK_CATEGORIES.map((category) => ({
            value: category,
            label: category,
        })),
        owner: [
            { value: 'region-global', label: 'Global' },
            { value: 'region-us', label: 'US' },
            { value: 'region-eu', label: 'EU' },
            { value: 'region-internal', label: 'Internal' },
        ],
    }

    // Filter frameworks based on current filters
    const filteredFrameworks = useMemo(() => {
        return frameworks.filter((framework) => {
            // Search filter
            if (filters.search) {
                const searchTerm = filters.search.toLowerCase()
                const matchesSearch =
                    framework.name.toLowerCase().includes(searchTerm) ||
                    framework.description.toLowerCase().includes(searchTerm) ||
                    framework.category.toLowerCase().includes(searchTerm)

                if (!matchesSearch) return false
            }

            // Status filter
            if (filters.status && filters.status.length > 0) {
                if (!filters.status.includes(framework.status)) return false
            }

            // Category filter
            if (filters.category && filters.category.length > 0) {
                if (!filters.category.includes(framework.category)) return false
            }

            // Region filter (mapped to owner in filter options)
            if (filters.owner && filters.owner.length > 0) {
                const regionFilters = filters.owner.map((owner) =>
                    owner.replace('region-', ''),
                )
                if (!regionFilters.includes(framework.region.toLowerCase()))
                    return false
            }

            return true
        })
    }, [frameworks, filters])

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="animate-pulse">
                    <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4"></div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {Array.from({ length: 6 }).map((_, i) => (
                            <div
                                key={i}
                                className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg"
                            ></div>
                        ))}
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 className="text-2xl font-bold">Framework Management</h1>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">
                        Manage compliance frameworks and track implementation
                        progress
                    </p>
                </div>
                <Button
                    variant="solid"
                    icon={<HiPlus />}
                    onClick={onAddFramework}
                    className="bg-emerald-500 hover:bg-emerald-600"
                >
                    Add Framework
                </Button>
            </div>

            {/* Filters */}
            <FilterBar
                searchPlaceholder="Search frameworks..."
                filters={filterOptions}
                onFilterChange={setFilters}
            />

            {/* Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="text-2xl font-bold text-emerald-600">
                        {frameworks.length}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        Total Frameworks
                    </div>
                </div>
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="text-2xl font-bold text-blue-600">
                        {frameworks.filter((f) => f.status === 'Active').length}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        Active
                    </div>
                </div>
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="text-2xl font-bold text-amber-600">
                        {
                            frameworks.filter((f) => f.status === 'In Progress')
                                .length
                        }
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        In Progress
                    </div>
                </div>
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="text-2xl font-bold text-purple-600">
                        {Math.round(
                            frameworks.reduce((acc, f) => acc + f.progress, 0) /
                                frameworks.length,
                        ) || 0}
                        %
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        Avg. Progress
                    </div>
                </div>
            </div>

            {/* Framework Grid */}
            {filteredFrameworks.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredFrameworks.map((framework) => (
                        <FrameworkCard
                            key={framework.id}
                            framework={framework}
                            onView={onView}
                            onEdit={onEdit}
                            onManageControls={onManageControls}
                            onViewReports={onViewReports}
                        />
                    ))}
                </div>
            ) : (
                <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                        <svg
                            className="w-16 h-16 mx-auto"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={1}
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                        </svg>
                    </div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                        No frameworks found
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                        {filters.search ||
                        filters.status ||
                        filters.category ||
                        filters.owner
                            ? 'Try adjusting your search or filters to find what you need.'
                            : 'Get started by adding your first compliance framework.'}
                    </p>
                    <Button
                        variant="solid"
                        icon={<HiPlus />}
                        onClick={onAddFramework}
                        className="bg-emerald-500 hover:bg-emerald-600"
                    >
                        Add Framework
                    </Button>
                </div>
            )}
        </div>
    )
}

export default FrameworkList
