'use client'

import { useState, useCallback } from 'react'
import {
    useVendors as useVendorsBase,
    useSupabaseMutation,
} from '@/shared/hooks'
import {
    Vendor,
    FilterOptions,
    PaginationOptions,
} from '@/shared/types/compliance'
import { exportToCSV } from '@/shared/utils/compliance'

export const useVendors = (
    filters?: FilterOptions,
    pagination?: PaginationOptions,
) => {
    return useVendorsBase(filters, pagination)
}

export const useVendorMutations = () => {
    const { create, update, remove, loading, error } =
        useSupabaseMutation<Vendor>('vendors')

    const createVendor = useCallback(
        async (vendorData: Partial<Vendor>) => {
            const newVendor = {
                ...vendorData,
                id: crypto.randomUUID(),
                status: 'Active' as const,
                riskLevel: vendorData.riskLevel || ('Medium' as const),
                controlIds: vendorData.controlIds || [],
                documents: vendorData.documents || [],
                tags: vendorData.tags || [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                organizationId: 'default-org', // This should come from auth context
            }

            return await create(newVendor)
        },
        [create],
    )

    const updateVendor = useCallback(
        async (id: string, updates: Partial<Vendor>) => {
            const updatedData = {
                ...updates,
                updatedAt: new Date().toISOString(),
            }

            return await update(id, updatedData)
        },
        [update],
    )

    const deleteVendor = useCallback(
        async (id: string) => {
            return await remove(id)
        },
        [remove],
    )

    return {
        createVendor,
        updateVendor,
        deleteVendor,
        loading,
        error,
    }
}

export const useVendorExport = () => {
    const exportVendorsToCSV = useCallback((vendors: Vendor[]) => {
        const exportData = vendors.map((vendor) => ({
            'Vendor ID': vendor.id,
            Name: vendor.name,
            Category: vendor.category,
            Status: vendor.status,
            'Risk Level': vendor.riskLevel,
            'Contact Email': vendor.contactEmail || '',
            'Contact Phone': vendor.contactPhone || '',
            Website: vendor.website || '',
            Description: vendor.description || '',
            'Contract Start': vendor.contractStartDate || '',
            'Contract End': vendor.contractEndDate || '',
            'Last Assessment': vendor.lastAssessmentDate || '',
            'Next Assessment': vendor.nextAssessmentDate || '',
            Controls: vendor.controlIds.length,
            Documents: vendor.documents.length,
            Tags: vendor.tags.join(', '),
            'Created Date': vendor.createdAt,
            'Updated Date': vendor.updatedAt,
        }))

        exportToCSV(
            exportData,
            `vendors-export-${new Date().toISOString().split('T')[0]}`,
        )
    }, [])

    return { exportVendorsToCSV }
}

// Mock data for development - remove when Supabase is set up
export const useMockVendors = () => {
    const [vendors] = useState<Vendor[]>([
        {
            id: 'vendor-1',
            name: 'Stripe Inc.',
            contactEmail: '<EMAIL>',
            contactPhone: '******-926-2289',
            website: 'https://stripe.com',
            description:
                'Payment processing platform for online businesses. Handles credit card transactions and PCI compliance.',
            category: 'Technology',
            riskLevel: 'High',
            status: 'Active',
            contractStartDate: '2023-01-01',
            contractEndDate: '2024-12-31',
            lastAssessmentDate: '2023-06-15',
            nextAssessmentDate: '2024-06-15',
            controlIds: ['pci-12.8.1', 'pci-12.8.2'],
            documents: [
                {
                    id: 'doc-1',
                    vendorId: 'vendor-1',
                    name: 'SOC 2 Type II Report',
                    type: 'SOC 2',
                    expiryDate: '2024-06-30',
                    status: 'Current',
                    uploadedAt: '2023-07-01T00:00:00Z',
                    uploadedBy: 'admin',
                },
                {
                    id: 'doc-2',
                    vendorId: 'vendor-1',
                    name: 'PCI DSS AOC',
                    type: 'Security Assessment',
                    expiryDate: '2024-03-31',
                    status: 'Current',
                    uploadedAt: '2023-04-01T00:00:00Z',
                    uploadedBy: 'admin',
                },
            ],
            tags: ['payment-processing', 'pci-compliant', 'critical'],
            createdAt: '2023-01-01T00:00:00Z',
            updatedAt: '2023-06-15T00:00:00Z',
            organizationId: 'default-org',
        },
        {
            id: 'vendor-2',
            name: 'AWS (Amazon Web Services)',
            contactEmail: '<EMAIL>',
            website: 'https://aws.amazon.com',
            description:
                'Cloud infrastructure provider hosting our cardholder data environment and payment applications.',
            category: 'Infrastructure',
            riskLevel: 'High',
            status: 'Active',
            contractStartDate: '2022-06-01',
            contractEndDate: '2025-05-31',
            lastAssessmentDate: '2023-11-01',
            nextAssessmentDate: '2024-11-01',
            controlIds: ['pci-2.4.1', 'pci-12.8.1'],
            documents: [
                {
                    id: 'doc-3',
                    vendorId: 'vendor-2',
                    name: 'AWS PCI DSS Responsibility Matrix',
                    type: 'Security Assessment',
                    expiryDate: '2024-12-31',
                    status: 'Current',
                    uploadedAt: '2023-01-15T00:00:00Z',
                    uploadedBy: 'admin',
                },
            ],
            tags: ['cloud-provider', 'infrastructure', 'pci-compliant'],
            createdAt: '2022-06-01T00:00:00Z',
            updatedAt: '2023-11-01T00:00:00Z',
            organizationId: 'default-org',
        },
        {
            id: 'vendor-3',
            name: 'CyberSecurity Consulting LLC',
            contactEmail: '<EMAIL>',
            contactPhone: '******-123-4567',
            website: 'https://cybersec-consulting.com',
            description:
                'Third-party security consulting firm providing penetration testing and vulnerability assessments.',
            category: 'Professional Services',
            riskLevel: 'Medium',
            status: 'Active',
            contractStartDate: '2023-03-01',
            contractEndDate: '2024-02-29',
            lastAssessmentDate: '2023-03-01',
            nextAssessmentDate: '2024-01-15',
            controlIds: ['pci-11.3.1', 'pci-11.3.2'],
            documents: [
                {
                    id: 'doc-4',
                    vendorId: 'vendor-3',
                    name: 'Professional Services Agreement',
                    type: 'Contract',
                    status: 'Current',
                    uploadedAt: '2023-03-01T00:00:00Z',
                    uploadedBy: 'admin',
                },
            ],
            tags: ['penetration-testing', 'security-consulting', 'quarterly'],
            createdAt: '2023-03-01T00:00:00Z',
            updatedAt: '2023-03-01T00:00:00Z',
            organizationId: 'default-org',
        },
        {
            id: 'vendor-4',
            name: 'DataBackup Solutions Inc.',
            contactEmail: '<EMAIL>',
            contactPhone: '******-555-0199',
            website: 'https://databackup.com',
            description:
                'Backup and disaster recovery services for critical business data including cardholder information.',
            category: 'Technology',
            riskLevel: 'Medium',
            status: 'Under Review',
            contractStartDate: '2023-01-15',
            contractEndDate: '2024-01-14',
            lastAssessmentDate: '2023-01-15',
            nextAssessmentDate: '2024-01-01',
            controlIds: ['pci-3.4.1', 'pci-12.8.1'],
            documents: [
                {
                    id: 'doc-5',
                    vendorId: 'vendor-4',
                    name: 'Security Questionnaire',
                    type: 'Security Assessment',
                    expiryDate: '2024-01-31',
                    status: 'Pending Review',
                    uploadedAt: '2023-12-01T00:00:00Z',
                    uploadedBy: 'admin',
                },
            ],
            tags: ['backup', 'disaster-recovery', 'data-protection'],
            createdAt: '2023-01-15T00:00:00Z',
            updatedAt: '2023-12-01T00:00:00Z',
            organizationId: 'default-org',
        },
        {
            id: 'vendor-5',
            name: 'Legacy Payment Systems',
            contactEmail: '<EMAIL>',
            description:
                'Former payment processor that was terminated due to security concerns.',
            category: 'Technology',
            riskLevel: 'Critical',
            status: 'Terminated',
            contractStartDate: '2021-01-01',
            contractEndDate: '2023-06-30',
            lastAssessmentDate: '2023-05-01',
            controlIds: [],
            documents: [
                {
                    id: 'doc-6',
                    vendorId: 'vendor-5',
                    name: 'Termination Notice',
                    type: 'Contract',
                    status: 'Expired',
                    uploadedAt: '2023-06-30T00:00:00Z',
                    uploadedBy: 'admin',
                },
            ],
            tags: ['terminated', 'legacy', 'security-issues'],
            createdAt: '2021-01-01T00:00:00Z',
            updatedAt: '2023-06-30T00:00:00Z',
            organizationId: 'default-org',
        },
    ])

    return {
        data: vendors,
        loading: false,
        error: null,
        total: vendors.length,
        refetch: () => {},
    }
}
