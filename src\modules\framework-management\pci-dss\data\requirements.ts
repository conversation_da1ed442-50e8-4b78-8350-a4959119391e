import { PCIDSSRequirement, PCI_DSS_CATEGORIES } from '../types'

// PCI DSS 4.0.1 Requirements Data
export const PCI_DSS_REQUIREMENTS: PCIDSSRequirement[] = [
    // Requirement 1: Install and maintain network security controls
    {
        id: 'pci-dss-1.1.1',
        number: '1.1.1',
        title: 'Processes and mechanisms for installing and maintaining network security controls are defined and understood',
        description: 'All security policies and operational procedures that are identified in Requirement 1 are documented, kept up to date, and in use.',
        category: PCI_DSS_CATEGORIES['1'],
        requirements: [
            'Document all security policies and operational procedures for network security controls',
            'Keep documentation up to date',
            'Ensure procedures are in active use',
            'Make procedures available to affected personnel'
        ],
        testingProcedures: [
            'Examine documentation to verify policies and procedures are documented and current',
            'Interview personnel to verify procedures are implemented and understood',
            'Observe processes to verify procedures are followed'
        ],
        status: 'Not Started',
        evidenceRequired: [
            'Network security policy documentation',
            'Operational procedures documentation',
            'Training records',
            'Process implementation evidence'
        ],
        riskLevel: 'High',
        businessImpact: 'High',
        implementationEffort: 'Medium',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
    },
    {
        id: 'pci-dss-1.1.2',
        number: '1.1.2',
        title: 'Roles and responsibilities for performing activities in Requirement 1 are documented, assigned, and understood',
        description: 'Personnel know what they are responsible for and have appropriate access and authority to perform their assigned responsibilities.',
        category: PCI_DSS_CATEGORIES['1'],
        requirements: [
            'Document roles and responsibilities for network security',
            'Assign specific responsibilities to personnel',
            'Ensure personnel understand their roles',
            'Provide appropriate access and authority'
        ],
        testingProcedures: [
            'Examine documentation of roles and responsibilities',
            'Interview personnel to verify understanding of roles',
            'Verify personnel have appropriate access for their responsibilities'
        ],
        status: 'Not Started',
        evidenceRequired: [
            'Role and responsibility matrix',
            'Job descriptions',
            'Access control documentation',
            'Training completion records'
        ],
        riskLevel: 'Medium',
        businessImpact: 'Medium',
        implementationEffort: 'Low',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
    },
    
    // Requirement 2: Apply secure configurations to all system components
    {
        id: 'pci-dss-2.1.1',
        number: '2.1.1',
        title: 'Processes and mechanisms for applying secure configurations to all system components are defined and understood',
        description: 'All security policies and operational procedures for secure configurations are documented, kept up to date, and in use.',
        category: PCI_DSS_CATEGORIES['2'],
        requirements: [
            'Document secure configuration policies and procedures',
            'Maintain current documentation',
            'Implement documented procedures',
            'Ensure personnel access to relevant procedures'
        ],
        testingProcedures: [
            'Examine secure configuration documentation',
            'Interview personnel about configuration procedures',
            'Observe configuration processes in practice'
        ],
        status: 'Not Started',
        evidenceRequired: [
            'Secure configuration standards',
            'Configuration management procedures',
            'Implementation evidence',
            'Personnel training records'
        ],
        riskLevel: 'High',
        businessImpact: 'High',
        implementationEffort: 'Medium',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
    },

    // Requirement 3: Protect stored cardholder data
    {
        id: 'pci-dss-3.1.1',
        number: '3.1.1',
        title: 'Processes and mechanisms for protecting stored cardholder data are defined and understood',
        description: 'All security policies and operational procedures for protecting stored cardholder data are documented, kept up to date, and in use.',
        category: PCI_DSS_CATEGORIES['3'],
        requirements: [
            'Document data protection policies and procedures',
            'Keep documentation current and accessible',
            'Implement documented procedures consistently',
            'Train personnel on data protection requirements'
        ],
        testingProcedures: [
            'Review data protection documentation',
            'Interview personnel about data handling procedures',
            'Observe data protection processes',
            'Verify training completion'
        ],
        status: 'Not Started',
        evidenceRequired: [
            'Data protection policy',
            'Data handling procedures',
            'Encryption implementation documentation',
            'Personnel training records'
        ],
        riskLevel: 'Critical',
        businessImpact: 'High',
        implementationEffort: 'High',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
    },

    // Requirement 4: Protect cardholder data with strong cryptography during transmission over open, public networks
    {
        id: 'pci-dss-4.1.1',
        number: '4.1.1',
        title: 'Processes and mechanisms for protecting cardholder data with strong cryptography during transmission are defined and understood',
        description: 'All security policies and operational procedures for protecting transmitted cardholder data are documented, kept up to date, and in use.',
        category: PCI_DSS_CATEGORIES['4'],
        requirements: [
            'Document transmission security policies',
            'Define cryptographic requirements for data transmission',
            'Implement strong encryption protocols',
            'Maintain current security procedures'
        ],
        testingProcedures: [
            'Examine transmission security documentation',
            'Verify cryptographic implementation',
            'Test transmission security controls',
            'Interview personnel about transmission procedures'
        ],
        status: 'Not Started',
        evidenceRequired: [
            'Transmission security policy',
            'Cryptographic standards documentation',
            'Network security configuration',
            'Encryption implementation evidence'
        ],
        riskLevel: 'Critical',
        businessImpact: 'High',
        implementationEffort: 'High',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
    },

    // Requirement 5: Protect all systems and networks from malicious software
    {
        id: 'pci-dss-5.1.1',
        number: '5.1.1',
        title: 'Processes and mechanisms for protecting all systems and networks from malicious software are defined and understood',
        description: 'All security policies and operational procedures for anti-malware protection are documented, kept up to date, and in use.',
        category: PCI_DSS_CATEGORIES['5'],
        requirements: [
            'Document anti-malware policies and procedures',
            'Define malware protection requirements',
            'Implement comprehensive anti-malware solutions',
            'Maintain current protection mechanisms'
        ],
        testingProcedures: [
            'Review anti-malware documentation',
            'Verify anti-malware implementation',
            'Test malware detection capabilities',
            'Interview personnel about malware procedures'
        ],
        status: 'Not Started',
        evidenceRequired: [
            'Anti-malware policy',
            'Malware protection procedures',
            'Anti-malware software configuration',
            'Malware detection logs'
        ],
        riskLevel: 'High',
        businessImpact: 'High',
        implementationEffort: 'Medium',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
    },

    // Requirement 6: Develop and maintain secure systems and software
    {
        id: 'pci-dss-6.1.1',
        number: '6.1.1',
        title: 'Processes and mechanisms for developing and maintaining secure systems and software are defined and understood',
        description: 'All security policies and operational procedures for secure development are documented, kept up to date, and in use.',
        category: PCI_DSS_CATEGORIES['6'],
        requirements: [
            'Document secure development policies',
            'Define secure coding standards',
            'Implement secure development lifecycle',
            'Maintain vulnerability management procedures'
        ],
        testingProcedures: [
            'Examine secure development documentation',
            'Review coding standards and guidelines',
            'Verify secure development implementation',
            'Interview development personnel'
        ],
        status: 'Not Started',
        evidenceRequired: [
            'Secure development policy',
            'Coding standards documentation',
            'Development lifecycle procedures',
            'Vulnerability assessment reports'
        ],
        riskLevel: 'High',
        businessImpact: 'High',
        implementationEffort: 'High',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
    }
]

// Helper function to get requirements by category
export const getRequirementsByCategory = (category: string) => {
    return PCI_DSS_REQUIREMENTS.filter(req => req.category === PCI_DSS_CATEGORIES[category])
}

// Helper function to get requirement by ID
export const getRequirementById = (id: string) => {
    return PCI_DSS_REQUIREMENTS.find(req => req.id === id)
}

// Helper function to get requirements by status
export const getRequirementsByStatus = (status: string) => {
    return PCI_DSS_REQUIREMENTS.filter(req => req.status === status)
}
