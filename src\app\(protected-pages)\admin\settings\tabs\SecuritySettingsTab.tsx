'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import FormItem from '@/components/ui/Form/FormItem'
import FormContainer from '@/components/ui/Form/FormContainer'
import {
    HiOutlineShieldCheck,
    HiOutlineKey,
    HiOutlineClock,
} from 'react-icons/hi'

const SecuritySettingsTab = () => {
    const [settings, setSettings] = useState({
        requireMFA: false,
        passwordMinLength: 8,
        passwordRequireSpecialChars: true,
        passwordRequireNumbers: true,
        passwordRequireUppercase: true,
        sessionTimeout: 24,
        maxLoginAttempts: 5,
        lockoutDuration: 30,
        requirePasswordChange: false,
        passwordChangeInterval: 90,
    })

    const [loading, setLoading] = useState(false)

    const handleSave = async () => {
        setLoading(true)
        try {
            // TODO: Save security settings to backend
            console.log('Saving security settings:', settings)
            await new Promise((resolve) => setTimeout(resolve, 1000)) // Mock delay
        } catch (error) {
            console.error('Error saving security settings:', error)
        } finally {
            setLoading(false)
        }
    }

    const handleToggle = (field: string) => {
        setSettings((prev) => ({
            ...prev,
            [field]: !prev[field as keyof typeof prev],
        }))
    }

    const handleNumberChange = (field: string, value: number) => {
        setSettings((prev) => ({ ...prev, [field]: value }))
    }

    return (
        <div className="space-y-6">
            <div>
                <h3 className="text-lg font-semibold mb-2">
                    Security Settings
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                    Configure security policies and authentication requirements
                </p>
            </div>

            <Card>
                <div className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                        <HiOutlineShieldCheck className="w-5 h-5 text-emerald-500" />
                        <h4 className="text-md font-semibold">
                            Authentication Settings
                        </h4>
                    </div>
                    <FormContainer>
                        <div className="space-y-4">
                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            Require Multi-Factor Authentication
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Force all users to enable MFA
                                        </p>
                                    </div>
                                    <input
                                        type="checkbox"
                                        checked={settings.requireMFA}
                                        onChange={() =>
                                            handleToggle('requireMFA')
                                        }
                                        className="rounded border-gray-300"
                                    />
                                </label>
                            </FormItem>

                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            Session Timeout (hours)
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Automatically log out inactive users
                                        </p>
                                    </div>
                                    <input
                                        type="number"
                                        min="1"
                                        max="168"
                                        value={settings.sessionTimeout}
                                        onChange={(e) =>
                                            handleNumberChange(
                                                'sessionTimeout',
                                                parseInt(e.target.value),
                                            )
                                        }
                                        className="w-20 px-2 py-1 border rounded"
                                    />
                                </label>
                            </FormItem>
                        </div>
                    </FormContainer>
                </div>
            </Card>

            <Card>
                <div className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                        <HiOutlineKey className="w-5 h-5 text-blue-500" />
                        <h4 className="text-md font-semibold">
                            Password Policy
                        </h4>
                    </div>
                    <FormContainer>
                        <div className="space-y-4">
                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            Minimum Password Length
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Minimum number of characters
                                            required
                                        </p>
                                    </div>
                                    <input
                                        type="number"
                                        min="6"
                                        max="32"
                                        value={settings.passwordMinLength}
                                        onChange={(e) =>
                                            handleNumberChange(
                                                'passwordMinLength',
                                                parseInt(e.target.value),
                                            )
                                        }
                                        className="w-20 px-2 py-1 border rounded"
                                    />
                                </label>
                            </FormItem>

                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            Require Special Characters
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Password must contain special
                                            characters
                                        </p>
                                    </div>
                                    <input
                                        type="checkbox"
                                        checked={
                                            settings.passwordRequireSpecialChars
                                        }
                                        onChange={() =>
                                            handleToggle(
                                                'passwordRequireSpecialChars',
                                            )
                                        }
                                        className="rounded border-gray-300"
                                    />
                                </label>
                            </FormItem>

                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            Require Numbers
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Password must contain numbers
                                        </p>
                                    </div>
                                    <input
                                        type="checkbox"
                                        checked={
                                            settings.passwordRequireNumbers
                                        }
                                        onChange={() =>
                                            handleToggle(
                                                'passwordRequireNumbers',
                                            )
                                        }
                                        className="rounded border-gray-300"
                                    />
                                </label>
                            </FormItem>

                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            Require Uppercase Letters
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Password must contain uppercase
                                            letters
                                        </p>
                                    </div>
                                    <input
                                        type="checkbox"
                                        checked={
                                            settings.passwordRequireUppercase
                                        }
                                        onChange={() =>
                                            handleToggle(
                                                'passwordRequireUppercase',
                                            )
                                        }
                                        className="rounded border-gray-300"
                                    />
                                </label>
                            </FormItem>

                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            Force Password Changes
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Require users to change passwords
                                            periodically
                                        </p>
                                    </div>
                                    <input
                                        type="checkbox"
                                        checked={settings.requirePasswordChange}
                                        onChange={() =>
                                            handleToggle(
                                                'requirePasswordChange',
                                            )
                                        }
                                        className="rounded border-gray-300"
                                    />
                                </label>
                            </FormItem>

                            {settings.requirePasswordChange && (
                                <FormItem>
                                    <label className="flex items-center justify-between">
                                        <div>
                                            <span className="font-medium">
                                                Password Change Interval (days)
                                            </span>
                                            <p className="text-sm text-gray-500">
                                                How often users must change
                                                passwords
                                            </p>
                                        </div>
                                        <input
                                            type="number"
                                            min="30"
                                            max="365"
                                            value={
                                                settings.passwordChangeInterval
                                            }
                                            onChange={(e) =>
                                                handleNumberChange(
                                                    'passwordChangeInterval',
                                                    parseInt(e.target.value),
                                                )
                                            }
                                            className="w-20 px-2 py-1 border rounded"
                                        />
                                    </label>
                                </FormItem>
                            )}
                        </div>
                    </FormContainer>
                </div>
            </Card>

            <Card>
                <div className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                        <HiOutlineClock className="w-5 h-5 text-amber-500" />
                        <h4 className="text-md font-semibold">
                            Account Lockout Policy
                        </h4>
                    </div>
                    <FormContainer>
                        <div className="space-y-4">
                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            Max Login Attempts
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Number of failed attempts before
                                            lockout
                                        </p>
                                    </div>
                                    <input
                                        type="number"
                                        min="3"
                                        max="10"
                                        value={settings.maxLoginAttempts}
                                        onChange={(e) =>
                                            handleNumberChange(
                                                'maxLoginAttempts',
                                                parseInt(e.target.value),
                                            )
                                        }
                                        className="w-20 px-2 py-1 border rounded"
                                    />
                                </label>
                            </FormItem>

                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            Lockout Duration (minutes)
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            How long accounts remain locked
                                        </p>
                                    </div>
                                    <input
                                        type="number"
                                        min="5"
                                        max="1440"
                                        value={settings.lockoutDuration}
                                        onChange={(e) =>
                                            handleNumberChange(
                                                'lockoutDuration',
                                                parseInt(e.target.value),
                                            )
                                        }
                                        className="w-20 px-2 py-1 border rounded"
                                    />
                                </label>
                            </FormItem>
                        </div>
                    </FormContainer>
                </div>
            </Card>

            <div className="flex justify-end">
                <Button
                    variant="solid"
                    onClick={handleSave}
                    loading={loading}
                    className="bg-emerald-500 hover:bg-emerald-600"
                >
                    Save Security Settings
                </Button>
            </div>
        </div>
    )
}

export default SecuritySettingsTab
