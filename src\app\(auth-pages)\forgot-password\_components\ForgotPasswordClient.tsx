'use client'

import { useAuth } from '@/contexts/AuthContext'
import ForgotPassword from '@/components/auth/ForgotPassword'
import { toast } from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'
import type { OnForgotPasswordSubmitPayload } from '@/components/auth/ForgotPassword'

const ForgotPasswordClient = () => {
    const { resetPassword } = useAuth()

    const handleForgotPasswordSubmit = async ({
        values,
        setSubmitting,
        setMessage,
        setEmailSent,
    }: OnForgotPasswordSubmitPayload) => {
        try {
            setSubmitting(true)
            const { error } = await resetPassword(values.email)

            if (error) {
                setMessage(error.message || 'Failed to send reset email')
            } else {
                toast.push(
                    <Notification title="Email sent!" type="success">
                        We have sent you an email to reset your password
                    </Notification>,
                )
                setEmailSent(true)
            }
        } catch {
            setMessage('An unexpected error occurred')
        } finally {
            setSubmitting(false)
        }
    }

    return (
        <ForgotPassword onForgotPasswordSubmit={handleForgotPasswordSubmit} />
    )
}

export default ForgotPasswordClient
