'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Avatar from '@/components/ui/Avatar'
import { StatusBadge } from '@/shared/components'
import { User } from '../types'
import {
    HiOutlineMail,
    HiOutlineClock,
    HiOutlineCog,
    HiOutlineTrash,
    HiOutlineShieldCheck,
    HiOutlineUserGroup,
    HiOutlineBan,
    HiOutlineCheck,
    HiOutlineKey,
} from 'react-icons/hi'

interface UserCardProps {
    user: User
    onEdit?: (user: User) => void
    onDelete?: (user: User) => void
    onChangeRole?: (user: User, role: 'admin' | 'user') => void
    onChangeStatus?: (
        user: User,
        status: 'active' | 'inactive' | 'suspended',
    ) => void
    onViewPermissions?: (user: User) => void
    compact?: boolean
    currentUserId?: string
}

const UserCard = ({
    user,
    onEdit,
    onDelete,
    onChangeRole,
    onChangeStatus,
    onViewPermissions,
    compact = false,
    currentUserId,
}: UserCardProps) => {
    const [isHovered, setIsHovered] = useState(false)

    const isCurrentUser = currentUserId === user.id

    const getRoleColor = (role: string) => {
        return role === 'admin'
            ? 'bg-purple-100 text-purple-700 border-purple-200'
            : 'bg-blue-100 text-blue-700 border-blue-200'
    }

    const getStatusColor = (status: string) => {
        const colors = {
            active: 'bg-green-100 text-green-700 border-green-200',
            inactive: 'bg-gray-100 text-gray-700 border-gray-200',
            pending: 'bg-amber-100 text-amber-700 border-amber-200',
            suspended: 'bg-red-100 text-red-700 border-red-200',
        }
        return colors[status as keyof typeof colors] || colors['inactive']
    }

    const formatLastLogin = (lastLogin?: string) => {
        if (!lastLogin) return 'Never'
        return new Date(lastLogin).toLocaleDateString()
    }

    if (compact) {
        return (
            <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                    <Avatar size="sm" src={user.avatar} />
                    <div className="flex-1 min-w-0">
                        <h4 className="font-medium truncate">
                            {user.firstName} {user.lastName}
                        </h4>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                            <span>{user.email}</span>
                            <span>•</span>
                            <span
                                className={`px-2 py-1 rounded text-xs ${getRoleColor(user.role)}`}
                            >
                                {user.role}
                            </span>
                        </div>
                    </div>
                </div>
                <div className="flex items-center gap-2">
                    <StatusBadge status={user.status} size="sm" />
                </div>
            </div>
        )
    }

    return (
        <Card
            className="h-full transition-all duration-200 hover:shadow-lg"
            bodyClass="h-full flex flex-col p-4"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            {/* Header */}
            <div className="flex justify-between items-start mb-3">
                <div className="flex items-center gap-3">
                    <Avatar size="md" src={user.avatar} />
                    <div>
                        <h4 className="font-semibold">
                            {user.firstName} {user.lastName}
                        </h4>
                        <p className="text-sm text-gray-500">
                            @{user.userName}
                        </p>
                    </div>
                </div>
                <div className="flex flex-col gap-1">
                    <span
                        className={`px-2 py-1 rounded text-xs font-medium ${getRoleColor(user.role)}`}
                    >
                        {user.role === 'admin' ? 'Administrator' : 'User'}
                    </span>
                    <span
                        className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(user.status)}`}
                    >
                        {user.status}
                    </span>
                </div>
            </div>

            {/* User Info */}
            <div className="flex-1 space-y-3">
                <div className="flex items-center gap-2 text-sm">
                    <HiOutlineMail className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600 dark:text-gray-400">
                        {user.email}
                    </span>
                </div>

                <div className="flex items-center gap-2 text-sm">
                    <HiOutlineClock className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600 dark:text-gray-400">
                        Last login: {formatLastLogin(user.lastLogin)}
                    </span>
                </div>

                <div className="flex items-center gap-2 text-sm">
                    <HiOutlineUserGroup className="w-4 h-4 text-gray-500" />
                    <span className="text-gray-600 dark:text-gray-400">
                        {user.permissions?.length || 0} permissions
                    </span>
                </div>
            </div>

            {/* Footer Actions */}
            <div className="flex justify-between items-center pt-3 border-t border-gray-200 dark:border-gray-700 mt-3">
                <div className="text-xs text-gray-500">
                    Created: {new Date(user.createdAt).toLocaleDateString()}
                </div>

                {/* Action Buttons */}
                <div
                    className={`flex gap-1 transition-opacity duration-200 ${isHovered ? 'opacity-100' : 'opacity-0'}`}
                >
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineShieldCheck />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onViewPermissions?.(user)
                        }}
                        title="View Permissions"
                    />

                    {!isCurrentUser && (
                        <>
                            {user.status === 'active' ? (
                                <Button
                                    size="xs"
                                    variant="plain"
                                    icon={<HiOutlineBan />}
                                    onClick={(e) => {
                                        e.stopPropagation()
                                        onChangeStatus?.(user, 'inactive')
                                    }}
                                    title="Deactivate User"
                                />
                            ) : (
                                <Button
                                    size="xs"
                                    variant="plain"
                                    icon={<HiOutlineCheck />}
                                    onClick={(e) => {
                                        e.stopPropagation()
                                        onChangeStatus?.(user, 'active')
                                    }}
                                    title="Activate User"
                                />
                            )}

                            <Button
                                size="xs"
                                variant="plain"
                                icon={<HiOutlineKey />}
                                onClick={(e) => {
                                    e.stopPropagation()
                                    onChangeRole?.(
                                        user,
                                        user.role === 'admin'
                                            ? 'user'
                                            : 'admin',
                                    )
                                }}
                                title={`Change to ${user.role === 'admin' ? 'User' : 'Admin'}`}
                            />

                            <Button
                                size="xs"
                                variant="plain"
                                icon={<HiOutlineCog />}
                                onClick={(e) => {
                                    e.stopPropagation()
                                    onEdit?.(user)
                                }}
                                title="Edit User"
                            />

                            <Button
                                size="xs"
                                variant="plain"
                                icon={<HiOutlineTrash />}
                                onClick={(e) => {
                                    e.stopPropagation()
                                    onDelete?.(user)
                                }}
                                title="Delete User"
                                className="text-red-500 hover:text-red-600"
                            />
                        </>
                    )}
                </div>
            </div>
        </Card>
    )
}

export default UserCard
