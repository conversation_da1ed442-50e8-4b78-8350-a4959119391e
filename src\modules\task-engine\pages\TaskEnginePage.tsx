'use client'

import { TaskList } from '../components'
import { useMockTasks, useTaskMutations, useTaskExport } from '../hooks'
import { TaskDetail } from '../types'

const TaskEnginePage = () => {
    const { data: tasks, loading, error } = useMockTasks()
    const { startTask, completeTask } = useTaskMutations()
    const { exportTasksToCSV } = useTaskExport()

    const handleViewTask = (task: TaskDetail) => {
        console.log('View task:', task)
        // TODO: Navigate to task detail page or open modal
    }

    const handleEditTask = (task: TaskDetail) => {
        console.log('Edit task:', task)
        // TODO: Open edit modal or navigate to edit page
    }

    const handleStartTask = async (task: TaskDetail) => {
        console.log('Start task:', task)
        try {
            await startTask(task.id)
            // TODO: Show success message and refresh data
        } catch (error) {
            console.error('Failed to start task:', error)
            // TODO: Show error message
        }
    }

    const handleCompleteTask = async (task: TaskDetail) => {
        console.log('Complete task:', task)
        try {
            await completeTask(task.id)
            // TODO: Show success message and refresh data
        } catch (error) {
            console.error('Failed to complete task:', error)
            // TODO: Show error message
        }
    }

    const handleCommentTask = (task: TaskDetail) => {
        console.log('Comment on task:', task)
        // TODO: Open comment modal or navigate to task detail
    }

    const handleAddTask = () => {
        console.log('Add new task')
        // TODO: Open add task modal or navigate to add page
    }

    const handleExportCSV = () => {
        exportTasksToCSV(tasks)
    }

    if (error) {
        return (
            <div className="p-6">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h3 className="text-red-800 font-medium">
                        Error loading tasks
                    </h3>
                    <p className="text-red-600 text-sm mt-1">{error}</p>
                </div>
            </div>
        )
    }

    return (
        <div className="p-6">
            <TaskList
                tasks={tasks}
                loading={loading}
                onView={handleViewTask}
                onEdit={handleEditTask}
                onStart={handleStartTask}
                onComplete={handleCompleteTask}
                onComment={handleCommentTask}
                onAddTask={handleAddTask}
                onExportCSV={handleExportCSV}
            />
        </div>
    )
}

export default TaskEnginePage
