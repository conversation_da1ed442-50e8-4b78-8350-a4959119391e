# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://uemjkvhfawncjpesfupm.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.zSBXphxu0nv9szeA0U0k_iu-QOcpRhWoOljX-cQtVXE

# NextAuth Configuration (keeping for backward compatibility)
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# OAuth Providers (configure these in Supabase dashboard)
GOOGLE_AUTH_CLIENT_ID=your_google_client_id
GOOGLE_AUTH_CLIENT_SECRET=your_google_client_secret

GITHUB_AUTH_CLIENT_ID=your_github_client_id
GITHUB_AUTH_CLIENT_SECRET=your_github_client_secret

# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000/api
