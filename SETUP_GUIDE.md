# 🚀 **Compliance Platform Setup Guide**

This guide will help you set up your modular compliance platform with Supabase database integration.

## 📋 **Prerequisites**

- Node.js 18+ installed
- A Supabase account and project
- PostgreSQL client (optional, for manual database operations)

## 🗄️ **Step 1: Supabase Database Setup**

### 1.1 Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and create a new project
2. Wait for the project to be fully provisioned
3. Note down your project URL and keys

### 1.2 Apply Database Schema

1. Go to your Supabase dashboard → SQL Editor
2. Copy the contents of `database-schema.sql`
3. Paste and run the SQL to create all tables and relationships

**OR** use the PostgreSQL client:

```bash
psql -h db.your-project-ref.supabase.co -U postgres -d postgres -f database-schema.sql
```

### 1.3 Configure Environment Variables

Create a `.env.local` file in your project root:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key

# Optional: For automated setup script
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
```

## 🤖 **Step 2: Automated Setup (Recommended)**

### 2.1 Install Script Dependencies

```bash
cd scripts
npm install
```

### 2.2 Run Setup Script

```bash
cd scripts
npm run setup
```

This will:

- ✅ Test your Supabase connection
- ✅ Create a default organization
- ✅ Insert sample frameworks (PCI DSS 4.0.1, ISO 27001)
- ✅ Add sample controls, tasks, and risks
- ✅ Set up initial data for testing

## 🔧 **Step 3: Development Setup**

### 3.1 Install Dependencies

```bash
npm install
```

### 3.2 Start Development Server

```bash
npm run dev
```

### 3.3 Access the Platform

Open your browser and navigate to:

- **Dashboard**: `http://localhost:3000/dashboard/compliance-dashboard`
- **Framework Management**: `http://localhost:3000/dashboard/framework-management`
- **Task Engine**: `http://localhost:3000/dashboard/task-engine`
- **Risk Management**: `http://localhost:3000/dashboard/risk-management`

## 🧪 **Step 4: Testing the Modules**

### 4.1 Framework Management

- View the sample frameworks (PCI DSS 4.0.1, ISO 27001)
- Test filtering and search functionality
- Check progress tracking

### 4.2 Task Engine

- View recurring tasks (quarterly scans, monthly reviews)
- Test task filtering by status, priority, type
- Try the CSV export functionality

### 4.3 Risk Management

- View sample risks with different severity levels
- Test risk filtering and categorization
- Check risk score calculations

### 4.4 Compliance Dashboard

- View high-level metrics across all modules
- Check alerts and recent activity
- Verify metric calculations

## 🔄 **Step 5: Switching from Mock to Real Data**

Once you've tested with mock data, switch to real Supabase data:

### 5.1 Update Framework Management

In `src/modules/framework-management/pages/FrameworkManagementPage.tsx`:

```typescript
// Replace this line:
const { data: frameworks, loading, error } = useMockFrameworks()

// With this:
const { data: frameworks, loading, error } = useFrameworks()
```

### 5.2 Update Task Engine

In `src/modules/task-engine/pages/TaskEnginePage.tsx`:

```typescript
// Replace this line:
const { data: tasks, loading, error } = useMockTasks()

// With this:
const { data: tasks, loading, error } = useTasks()
```

### 5.3 Update Risk Management

In `src/modules/risk-management/pages/RiskManagementPage.tsx`:

```typescript
// Replace this line:
const { data: risks, loading, error } = useMockRisks()

// With this:
const { data: risks, loading, error } = useRisks()
```

## 🔐 **Step 6: Authentication Setup (Optional)**

To add user authentication:

### 6.1 Configure Supabase Auth

1. Go to Supabase Dashboard → Authentication → Settings
2. Configure your site URL: `http://localhost:3000`
3. Enable email authentication or social providers

### 6.2 Add Auth Context

Create `src/contexts/AuthContext.tsx` and wrap your app with authentication.

### 6.3 Update RLS Policies

Modify the Row Level Security policies in your database to use actual user IDs instead of the mock organization ID.

## 📊 **Step 7: Customization**

### 7.1 Add Your Frameworks

1. Go to Framework Management
2. Click "Add Framework"
3. Configure your specific compliance requirements

### 7.2 Configure Controls

1. Add controls specific to your frameworks
2. Map controls to your business processes
3. Set up evidence requirements

### 7.3 Set Up Recurring Tasks

1. Create quarterly vulnerability scans
2. Set up monthly access reviews
3. Configure annual policy reviews

## 🚨 **Troubleshooting**

### Database Connection Issues

```bash
# Test your connection
psql -h db.your-project-ref.supabase.co -U postgres -d postgres -c "SELECT 1;"
```

### Missing Tables Error

- Ensure you've run the `database-schema.sql` file
- Check Supabase Dashboard → Table Editor to verify tables exist

### Environment Variables

- Verify your `.env.local` file has the correct Supabase credentials
- Restart your development server after changing environment variables

### Module Not Loading

- Check the browser console for JavaScript errors
- Verify all imports are correct in the module files

## 📈 **Next Steps**

### Immediate (Week 1)

- [ ] Set up your Supabase database
- [ ] Test all modules with sample data
- [ ] Add your first real framework
- [ ] Create your first recurring tasks

### Short Term (Month 1)

- [ ] Switch to real data instead of mock data
- [ ] Add user authentication
- [ ] Customize for your specific compliance needs
- [ ] Set up the remaining modules (Vendor Management, Policy Tracker, etc.)

### Long Term (Quarter 1)

- [ ] Implement advanced reporting
- [ ] Add email notifications for overdue tasks
- [ ] Create custom dashboards
- [ ] Integrate with external tools (Slack, Teams)

## 🆘 **Support**

If you encounter issues:

1. **Check the logs**: Browser console and terminal output
2. **Verify environment**: Ensure all environment variables are set
3. **Database access**: Confirm Supabase connection works
4. **Module structure**: Verify all files are in the correct locations

## 🎯 **Success Criteria**

You'll know the setup is successful when:

- ✅ All dashboard modules load without errors
- ✅ Sample data appears in the interface
- ✅ Filtering and search work correctly
- ✅ Navigation between modules works
- ✅ CSV export functions work
- ✅ Metrics display correctly on the dashboard

---

**🎉 Congratulations!** You now have a fully functional modular compliance platform focused on PCI DSS 4.0.1 and other frameworks. The platform is designed to be manual-first, giving you complete control over your compliance processes.
