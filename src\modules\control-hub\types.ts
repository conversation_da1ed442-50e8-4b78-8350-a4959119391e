// Import types first
import type {
    Control,
    Framework,
    Task,
    Risk,
    Policy,
    Evidence,
    FilterOptions,
    PaginationOptions,
} from '@/shared/types/compliance'

// Control Hub module types
export interface ControlDetail extends Control {
    framework?: Framework
    tasks: Task[]
    risks: Risk[]
    policies: Policy[]
    evidence: Evidence[]
    lastTestDate?: string
    nextTestDate?: string
    testResults?: TestResult[]
    implementationNotes?: string
    compensatingControls?: Control[]
}

export interface TestResult {
    id: string
    controlId: string
    testDate: string
    testType: 'Manual' | 'Automated' | 'Walkthrough' | 'Inspection'
    result: 'Pass' | 'Fail' | 'Not Applicable' | 'Exception'
    findings?: string
    recommendations?: string
    tester: string
    evidence?: string[]
}

export interface ControlMapping {
    id: string
    sourceControlId: string
    targetControlId: string
    sourceFramework: string
    targetFramework: string
    mappingType: 'Equivalent' | 'Partial' | 'Related' | 'Covers'
    notes?: string
}

export interface ControlFilter extends FilterOptions {
    frameworkId?: string
    riskLevel?: string[]
    evidenceReadiness?: string[]
    implementationEffort?: string[]
    businessImpact?: string[]
    testStatus?: string[]
}

// Re-export shared types for convenience
export type {
    Control,
    Framework,
    Task,
    Risk,
    Policy,
    Evidence,
    FilterOptions,
    PaginationOptions,
}
