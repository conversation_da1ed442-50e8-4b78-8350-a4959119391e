import { createServerClient } from '@supabase/ssr'
import {
    authRoutes as _authRoutes,
    publicRoutes as _publicRoutes,
} from '@/configs/routes.config'
import { REDIRECT_URL_KEY } from '@/constants/app.constant'
import appConfig from '@/configs/app.config'
import { NextRequest, NextResponse } from 'next/server'

const publicRoutes = Object.entries(_publicRoutes).map(([key]) => key)
const authRoutes = Object.entries(_authRoutes).map(([key]) => key)

const apiAuthPrefix = `${appConfig.apiPrefix}/auth`

export async function middleware(req: NextRequest) {
    const { nextUrl } = req

    // Create a response object to pass to Supabase
    // eslint-disable-next-line prefer-const
    let response = NextResponse.next({
        request: {
            headers: req.headers,
        },
    })

    // Create Supabase client
    const supabase = createServerClient(
        appConfig.supabase.url,
        appConfig.supabase.anonKey,
        {
            cookies: {
                getAll() {
                    return req.cookies.getAll()
                },
                setAll(cookiesToSet) {
                    cookiesToSet.forEach(({ name, value, options }) => {
                        req.cookies.set(name, value)
                        response.cookies.set(name, value, options)
                    })
                },
            },
        },
    )

    // Get user session
    const {
        data: { user },
    } = await supabase.auth.getUser()
    const isSignedIn = !!user

    const isApiAuthRoute = nextUrl.pathname.startsWith(apiAuthPrefix)
    const isPublicRoute = publicRoutes.includes(nextUrl.pathname)
    const isAuthRoute = authRoutes.includes(nextUrl.pathname)

    /** Skip auth middleware for api routes */
    if (isApiAuthRoute) return response

    if (isAuthRoute) {
        if (isSignedIn) {
            /** Redirect to authenticated entry path if signed in & path is auth route */
            return NextResponse.redirect(
                new URL(appConfig.authenticatedEntryPath, nextUrl.origin),
            )
        }
        return response
    }

    /** Redirect to authenticated entry path if signed in & path is public route */
    if (!isSignedIn && !isPublicRoute) {
        let callbackUrl = nextUrl.pathname
        if (nextUrl.search) {
            callbackUrl += nextUrl.search
        }

        return NextResponse.redirect(
            new URL(
                `${appConfig.unAuthenticatedEntryPath}?${REDIRECT_URL_KEY}=${callbackUrl}`,
                nextUrl.origin,
            ),
        )
    }

    return response
}

export const config = {
    matcher: ['/((?!.+\\.[\\w]+$|_next).*)', '/', '/(api)(.*)'],
}
