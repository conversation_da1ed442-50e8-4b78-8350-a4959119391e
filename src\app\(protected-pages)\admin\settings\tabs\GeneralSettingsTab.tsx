'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Select from '@/components/ui/Select'
import FormItem from '@/components/ui/Form/FormItem'
import FormContainer from '@/components/ui/Form/FormContainer'

const GeneralSettingsTab = () => {
    const [settings, setSettings] = useState({
        organizationName: 'CheckGap Inc.',
        organizationDomain: 'checkgap.com',
        timezone: 'UTC',
        dateFormat: 'MM/DD/YYYY',
        language: 'en',
        defaultRole: 'user',
    })

    const [loading, setLoading] = useState(false)

    const timezoneOptions = [
        { value: 'UTC', label: 'UTC' },
        { value: 'America/New_York', label: 'Eastern Time' },
        { value: 'America/Chicago', label: 'Central Time' },
        { value: 'America/Denver', label: 'Mountain Time' },
        { value: 'America/Los_Angeles', label: 'Pacific Time' },
        { value: 'Europe/London', label: 'London' },
        { value: 'Europe/Paris', label: 'Paris' },
        { value: 'Asia/Tokyo', label: 'Tokyo' },
    ]

    const dateFormatOptions = [
        { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
        { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
        { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' },
    ]

    const languageOptions = [
        { value: 'en', label: 'English' },
        { value: 'es', label: 'Spanish' },
        { value: 'fr', label: 'French' },
        { value: 'de', label: 'German' },
    ]

    const defaultRoleOptions = [
        { value: 'user', label: 'User' },
        { value: 'admin', label: 'Administrator' },
    ]

    const handleSave = async () => {
        setLoading(true)
        try {
            // TODO: Save settings to backend
            console.log('Saving settings:', settings)
            await new Promise((resolve) => setTimeout(resolve, 1000)) // Mock delay
        } catch (error) {
            console.error('Error saving settings:', error)
        } finally {
            setLoading(false)
        }
    }

    const handleInputChange = (field: string, value: string) => {
        setSettings((prev) => ({ ...prev, [field]: value }))
    }

    return (
        <div className="space-y-6">
            <div>
                <h3 className="text-lg font-semibold mb-2">General Settings</h3>
                <p className="text-gray-500 dark:text-gray-400">
                    Configure basic organization settings and preferences
                </p>
            </div>

            <Card>
                <div className="p-6">
                    <h4 className="text-md font-semibold mb-4">
                        Organization Information
                    </h4>
                    <FormContainer>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <FormItem label="Organization Name">
                                <Input
                                    value={settings.organizationName}
                                    onChange={(e) =>
                                        handleInputChange(
                                            'organizationName',
                                            e.target.value,
                                        )
                                    }
                                    placeholder="Your organization name"
                                />
                            </FormItem>

                            <FormItem label="Organization Domain">
                                <Input
                                    value={settings.organizationDomain}
                                    onChange={(e) =>
                                        handleInputChange(
                                            'organizationDomain',
                                            e.target.value,
                                        )
                                    }
                                    placeholder="yourcompany.com"
                                />
                            </FormItem>
                        </div>
                    </FormContainer>
                </div>
            </Card>

            <Card>
                <div className="p-6">
                    <h4 className="text-md font-semibold mb-4">
                        Regional & Language Settings
                    </h4>
                    <FormContainer>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <FormItem label="Timezone">
                                <Select
                                    options={timezoneOptions}
                                    value={timezoneOptions.find(
                                        (option) =>
                                            option.value === settings.timezone,
                                    )}
                                    onChange={(option) =>
                                        handleInputChange(
                                            'timezone',
                                            option?.value || 'UTC',
                                        )
                                    }
                                />
                            </FormItem>

                            <FormItem label="Date Format">
                                <Select
                                    options={dateFormatOptions}
                                    value={dateFormatOptions.find(
                                        (option) =>
                                            option.value ===
                                            settings.dateFormat,
                                    )}
                                    onChange={(option) =>
                                        handleInputChange(
                                            'dateFormat',
                                            option?.value || 'MM/DD/YYYY',
                                        )
                                    }
                                />
                            </FormItem>

                            <FormItem label="Language">
                                <Select
                                    options={languageOptions}
                                    value={languageOptions.find(
                                        (option) =>
                                            option.value === settings.language,
                                    )}
                                    onChange={(option) =>
                                        handleInputChange(
                                            'language',
                                            option?.value || 'en',
                                        )
                                    }
                                />
                            </FormItem>

                            <FormItem label="Default User Role">
                                <Select
                                    options={defaultRoleOptions}
                                    value={defaultRoleOptions.find(
                                        (option) =>
                                            option.value ===
                                            settings.defaultRole,
                                    )}
                                    onChange={(option) =>
                                        handleInputChange(
                                            'defaultRole',
                                            option?.value || 'user',
                                        )
                                    }
                                />
                            </FormItem>
                        </div>
                    </FormContainer>
                </div>
            </Card>

            <div className="flex justify-end">
                <Button
                    variant="solid"
                    onClick={handleSave}
                    loading={loading}
                    className="bg-emerald-500 hover:bg-emerald-600"
                >
                    Save Settings
                </Button>
            </div>
        </div>
    )
}

export default GeneralSettingsTab
