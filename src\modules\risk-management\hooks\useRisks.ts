'use client'

import { useState, useCallback } from 'react'
import { useRisks as useRisksBase, useSupabaseMutation } from '@/shared/hooks'
import {
    Risk,
    FilterOptions,
    PaginationOptions,
} from '@/shared/types/compliance'
import { calculateRiskScore, exportToCSV } from '@/shared/utils/compliance'

export const useRisks = (
    filters?: FilterOptions,
    pagination?: PaginationOptions,
) => {
    return useRisksBase(filters, pagination)
}

export const useRiskMutations = () => {
    const { create, update, remove, loading, error } =
        useSupabaseMutation<Risk>('risks')

    const createRisk = useCallback(
        async (riskData: Partial<Risk>) => {
            const riskScore = calculateRiskScore(
                riskData.likelihood || 1,
                riskData.impact || 1,
            )

            const newRisk = {
                ...riskData,
                id: crypto.randomUUID(),
                riskScore,
                status: 'Identified' as const,
                controlIds: riskData.controlIds || [],
                tags: riskData.tags || [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                organizationId: 'default-org', // This should come from auth context
            }

            return await create(newRisk)
        },
        [create],
    )

    const updateRisk = useCallback(
        async (id: string, updates: Partial<Risk>) => {
            const updatedData = {
                ...updates,
                updatedAt: new Date().toISOString(),
            }

            // Recalculate risk score if likelihood or impact changed
            if (updates.likelihood || updates.impact) {
                const currentRisk = await getRiskById(id) // You'd implement this
                updatedData.riskScore = calculateRiskScore(
                    updates.likelihood || currentRisk?.likelihood || 1,
                    updates.impact || currentRisk?.impact || 1,
                )
            }

            return await update(id, updatedData)
        },
        [update],
    )

    const deleteRisk = useCallback(
        async (id: string) => {
            return await remove(id)
        },
        [remove],
    )

    const mitigateRisk = useCallback(
        async (id: string, mitigationPlan: string) => {
            return await updateRisk(id, {
                status: 'Mitigated',
                mitigationPlan,
            })
        },
        [updateRisk],
    )

    return {
        createRisk,
        updateRisk,
        deleteRisk,
        mitigateRisk,
        loading,
        error,
    }
}

// Helper function - would be implemented properly with Supabase
const getRiskById = async (id: string): Promise<Risk | null> => {
    // This would fetch from Supabase
    console.log('Getting risk by id:', id)
    return null
}

export const useRiskExport = () => {
    const exportRisksToCSV = useCallback((risks: Risk[]) => {
        const exportData = risks.map((risk) => ({
            'Risk ID': risk.id,
            Title: risk.title,
            Description: risk.description,
            Category: risk.category,
            Status: risk.status,
            Likelihood: risk.likelihood,
            Impact: risk.impact,
            'Risk Score': risk.riskScore,
            'Inherent Risk': risk.inherentRisk || '',
            'Residual Risk': risk.residualRisk || '',
            Owner: risk.owner || '',
            Controls: risk.controlIds.length,
            'Mitigation Plan': risk.mitigationPlan || '',
            'Mitigation Deadline': risk.mitigationDeadline || '',
            'Last Review': risk.lastReviewDate || '',
            'Next Review': risk.nextReviewDate || '',
            Tags: risk.tags.join(', '),
            'Created Date': risk.createdAt,
            'Updated Date': risk.updatedAt,
        }))

        exportToCSV(
            exportData,
            `risks-export-${new Date().toISOString().split('T')[0]}`,
        )
    }, [])

    return { exportRisksToCSV }
}

// Mock data for development - remove when Supabase is set up
export const useMockRisks = () => {
    const [risks] = useState<Risk[]>([
        {
            id: 'risk-1',
            title: 'Cardholder Data Exposure via Unencrypted Storage',
            description:
                'Risk of cardholder data being exposed due to unencrypted storage in legacy systems that have not been updated to meet PCI DSS 4.0.1 requirements.',
            category: 'Compliance',
            likelihood: 3,
            impact: 5,
            riskScore: 15,
            inherentRisk: 20,
            residualRisk: 15,
            status: 'Assessed',
            owner: 'security-team',
            controlIds: ['pci-3.4.1', 'pci-3.5.1'],
            mitigationPlan:
                'Implement encryption for all cardholder data storage systems and update legacy systems to comply with PCI DSS 4.0.1',
            mitigationDeadline: '2024-03-31',
            lastReviewDate: '2024-01-01',
            nextReviewDate: '2024-04-01',
            tags: ['cardholder-data', 'encryption', 'legacy-systems'],
            createdAt: '2023-10-15T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
            organizationId: 'default-org',
        },
        {
            id: 'risk-2',
            title: 'Insider Threat - Privileged Access Abuse',
            description:
                'Risk of privileged users accessing cardholder data beyond their job requirements, potentially leading to data theft or misuse.',
            category: 'Operational',
            likelihood: 2,
            impact: 4,
            riskScore: 8,
            inherentRisk: 12,
            residualRisk: 8,
            status: 'Mitigated',
            owner: 'hr-team',
            controlIds: ['pci-7.1.1', 'pci-8.2.1'],
            mitigationPlan:
                'Implement role-based access controls and regular access reviews',
            mitigationDeadline: '2024-02-28',
            lastReviewDate: '2023-12-15',
            nextReviewDate: '2024-03-15',
            tags: ['insider-threat', 'access-control', 'privileged-access'],
            createdAt: '2023-09-01T00:00:00Z',
            updatedAt: '2023-12-15T00:00:00Z',
            organizationId: 'default-org',
        },
        {
            id: 'risk-3',
            title: 'Third-Party Vendor Data Breach',
            description:
                'Risk of cardholder data being compromised through a security breach at a third-party service provider that handles payment processing.',
            category: 'Technology',
            likelihood: 4,
            impact: 5,
            riskScore: 20,
            inherentRisk: 25,
            residualRisk: 20,
            status: 'Identified',
            owner: 'vendor-management',
            controlIds: ['pci-12.8.1', 'pci-12.8.2'],
            mitigationPlan:
                'Enhance vendor security assessments and implement continuous monitoring',
            mitigationDeadline: '2024-06-30',
            nextReviewDate: '2024-02-01',
            tags: ['third-party', 'vendor', 'data-breach'],
            createdAt: '2024-01-05T00:00:00Z',
            updatedAt: '2024-01-05T00:00:00Z',
            organizationId: 'default-org',
        },
        {
            id: 'risk-4',
            title: 'Network Segmentation Failure',
            description:
                'Risk of network segmentation controls failing, allowing unauthorized access to the cardholder data environment from less secure network segments.',
            category: 'Technology',
            likelihood: 2,
            impact: 4,
            riskScore: 8,
            inherentRisk: 16,
            residualRisk: 8,
            status: 'Mitigated',
            owner: 'network-team',
            controlIds: ['pci-1.2.1', 'pci-1.3.1'],
            mitigationPlan:
                'Implement redundant network segmentation controls and regular penetration testing',
            lastReviewDate: '2023-11-30',
            nextReviewDate: '2024-02-29',
            tags: ['network-segmentation', 'firewall', 'penetration-testing'],
            createdAt: '2023-08-15T00:00:00Z',
            updatedAt: '2023-11-30T00:00:00Z',
            organizationId: 'default-org',
        },
        {
            id: 'risk-5',
            title: 'Malware Infection in Payment Systems',
            description:
                'Risk of malware infecting payment processing systems, potentially leading to cardholder data theft or system compromise.',
            category: 'Technology',
            likelihood: 3,
            impact: 4,
            riskScore: 12,
            inherentRisk: 16,
            residualRisk: 12,
            status: 'Assessed',
            owner: 'security-team',
            controlIds: ['pci-5.1.1', 'pci-5.2.1'],
            mitigationPlan:
                'Deploy advanced anti-malware solutions and implement application whitelisting',
            mitigationDeadline: '2024-04-30',
            lastReviewDate: '2023-12-01',
            nextReviewDate: '2024-03-01',
            tags: ['malware', 'payment-systems', 'anti-virus'],
            createdAt: '2023-11-01T00:00:00Z',
            updatedAt: '2023-12-01T00:00:00Z',
            organizationId: 'default-org',
        },
    ])

    return {
        data: risks,
        loading: false,
        error: null,
        total: risks.length,
        refetch: () => {},
    }
}
