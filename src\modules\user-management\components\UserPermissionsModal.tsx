'use client'

import { useState, useEffect } from 'react'
import Dialog from '@/components/ui/Dialog'
import But<PERSON> from '@/components/ui/Button'
import FormItem from '@/components/ui/Form/FormItem'
import FormContainer from '@/components/ui/Form/FormContainer'
import {
    User,
    UserPermissions,
    DEFAULT_USER_PERMISSIONS,
    DEFAULT_ADMIN_PERMISSIONS,
} from '../types'
import { HiOutlineX, HiOutlineShieldCheck, HiOutlineUser } from 'react-icons/hi'

interface UserPermissionsModalProps {
    isOpen: boolean
    onClose: () => void
    user: User | null
    onSave?: (userId: string, permissions: string[]) => Promise<void>
    loading?: boolean
}

const UserPermissionsModal = ({
    isOpen,
    onClose,
    user,
    onSave,
    loading = false,
}: UserPermissionsModalProps) => {
    const [permissions, setPermissions] = useState<UserPermissions>(
        DEFAULT_USER_PERMISSIONS,
    )

    useEffect(() => {
        if (user) {
            // Convert user permissions array to permissions object
            const userPermissions = { ...DEFAULT_USER_PERMISSIONS }

            if (user.permissions) {
                Object.keys(userPermissions).forEach((key) => {
                    userPermissions[key as keyof UserPermissions] =
                        user.permissions!.includes(key)
                })
            }

            setPermissions(userPermissions)
        }
    }, [user])

    const handlePermissionChange = (
        permission: keyof UserPermissions,
        value: boolean,
    ) => {
        setPermissions((prev) => ({
            ...prev,
            [permission]: value,
        }))
    }

    const handleSave = async () => {
        if (!user || !onSave) return

        const enabledPermissions = Object.entries(permissions)
            .filter(([, enabled]) => enabled)
            .map(([permission]) => permission)

        try {
            await onSave(user.id, enabledPermissions)
            onClose()
        } catch (error) {
            console.error('Error saving permissions:', error)
        }
    }

    const handleSetDefaultRole = (role: 'admin' | 'user') => {
        const defaultPermissions =
            role === 'admin'
                ? DEFAULT_ADMIN_PERMISSIONS
                : DEFAULT_USER_PERMISSIONS
        setPermissions(defaultPermissions)
    }

    const permissionGroups = [
        {
            title: 'Framework Management',
            permissions: [
                { key: 'read:frameworks', label: 'View Frameworks' },
                { key: 'write:frameworks', label: 'Edit Frameworks' },
                { key: 'delete:frameworks', label: 'Delete Frameworks' },
            ],
        },
        {
            title: 'Control Management',
            permissions: [
                { key: 'read:controls', label: 'View Controls' },
                { key: 'write:controls', label: 'Edit Controls' },
                { key: 'delete:controls', label: 'Delete Controls' },
            ],
        },
        {
            title: 'Task Management',
            permissions: [
                { key: 'read:tasks', label: 'View Tasks' },
                { key: 'write:tasks', label: 'Edit Tasks' },
                { key: 'delete:tasks', label: 'Delete Tasks' },
            ],
        },
        {
            title: 'Risk Management',
            permissions: [
                { key: 'read:risks', label: 'View Risks' },
                { key: 'write:risks', label: 'Edit Risks' },
                { key: 'delete:risks', label: 'Delete Risks' },
            ],
        },
        {
            title: 'Vendor Management',
            permissions: [
                { key: 'read:vendors', label: 'View Vendors' },
                { key: 'write:vendors', label: 'Edit Vendors' },
                { key: 'delete:vendors', label: 'Delete Vendors' },
            ],
        },
        {
            title: 'Policy Management',
            permissions: [
                { key: 'read:policies', label: 'View Policies' },
                { key: 'write:policies', label: 'Edit Policies' },
                { key: 'delete:policies', label: 'Delete Policies' },
            ],
        },
        {
            title: 'Asset Management',
            permissions: [
                { key: 'read:assets', label: 'View Assets' },
                { key: 'write:assets', label: 'Edit Assets' },
                { key: 'delete:assets', label: 'Delete Assets' },
            ],
        },
        {
            title: 'Evidence Management',
            permissions: [
                { key: 'read:evidence', label: 'View Evidence' },
                { key: 'write:evidence', label: 'Edit Evidence' },
                { key: 'delete:evidence', label: 'Delete Evidence' },
            ],
        },
        {
            title: 'Administration',
            permissions: [
                { key: 'read:users', label: 'View Users' },
                { key: 'write:users', label: 'Manage Users' },
                { key: 'delete:users', label: 'Delete Users' },
                { key: 'read:admin', label: 'View Admin Settings' },
                { key: 'write:admin', label: 'Manage Admin Settings' },
            ],
        },
    ]

    return (
        <Dialog isOpen={isOpen} onClose={onClose} width={700}>
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-3">
                    <HiOutlineShieldCheck className="w-5 h-5 text-emerald-500" />
                    <h3 className="text-lg font-semibold">User Permissions</h3>
                </div>
                <Button
                    variant="plain"
                    size="sm"
                    icon={<HiOutlineX />}
                    onClick={onClose}
                />
            </div>

            <div className="p-6">
                {user && (
                    <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div className="flex items-center gap-3 mb-2">
                            <HiOutlineUser className="w-5 h-5 text-gray-500" />
                            <span className="font-medium">
                                {user.firstName} {user.lastName}
                            </span>
                            <span className="text-sm text-gray-500">
                                ({user.email})
                            </span>
                        </div>
                        <div className="flex gap-2">
                            <Button
                                size="sm"
                                variant="plain"
                                onClick={() => handleSetDefaultRole('user')}
                                className="text-blue-600 hover:text-blue-700"
                            >
                                Set User Defaults
                            </Button>
                            <Button
                                size="sm"
                                variant="plain"
                                onClick={() => handleSetDefaultRole('admin')}
                                className="text-purple-600 hover:text-purple-700"
                            >
                                Set Admin Defaults
                            </Button>
                        </div>
                    </div>
                )}

                <FormContainer>
                    <div className="space-y-6 max-h-96 overflow-y-auto">
                        {permissionGroups.map((group) => (
                            <div key={group.title} className="space-y-3">
                                <h4 className="font-medium text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
                                    {group.title}
                                </h4>
                                <div className="space-y-2 pl-4">
                                    {group.permissions.map((permission) => (
                                        <FormItem key={permission.key}>
                                            <label className="flex items-center justify-between">
                                                <span className="text-sm">
                                                    {permission.label}
                                                </span>
                                                <input
                                                    type="checkbox"
                                                    checked={
                                                        permissions[
                                                            permission.key as keyof UserPermissions
                                                        ]
                                                    }
                                                    onChange={(e) =>
                                                        handlePermissionChange(
                                                            permission.key as keyof UserPermissions,
                                                            e.target.checked,
                                                        )
                                                    }
                                                    className="rounded border-gray-300"
                                                />
                                            </label>
                                        </FormItem>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                </FormContainer>
            </div>

            <div className="flex justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
                <Button
                    type="button"
                    variant="plain"
                    onClick={onClose}
                    disabled={loading}
                >
                    Cancel
                </Button>
                <Button
                    type="button"
                    variant="solid"
                    onClick={handleSave}
                    loading={loading}
                    className="bg-emerald-500 hover:bg-emerald-600"
                >
                    Save Permissions
                </Button>
            </div>
        </Dialog>
    )
}

export default UserPermissionsModal
