'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { StatusBadge } from '@/shared/components'
import { Vendor } from '@/shared/types/compliance'
import {
    HiOutlineOfficeBuilding,
    HiOutlineGlobe,
    HiOutlineDocumentText,
    HiOutlineCalendar,
    HiOutlineEye,
    HiOutlineCog,
    HiOutlineExclamationCircle,
    HiOutlineShieldCheck,
} from 'react-icons/hi'

interface VendorCardProps {
    vendor: Vendor
    onView?: (vendor: Vendor) => void
    onEdit?: (vendor: Vendor) => void
    onViewDocuments?: (vendor: Vendor) => void
    onViewControls?: (vendor: Vendor) => void
    compact?: boolean
}

const VendorCard = ({
    vendor,
    onView,
    onEdit,
    onViewDocuments,
    onViewControls,
    compact = false,
}: VendorCardProps) => {
    const [isHovered, setIsHovered] = useState(false)

    const getCategoryColor = (category: string) => {
        const colors = {
            Technology: 'bg-blue-100 text-blue-700 border-blue-200',
            'Professional Services':
                'bg-purple-100 text-purple-700 border-purple-200',
            Infrastructure: 'bg-green-100 text-green-700 border-green-200',
            Financial: 'bg-amber-100 text-amber-700 border-amber-200',
            Other: 'bg-gray-100 text-gray-700 border-gray-200',
        }
        return colors[category as keyof typeof colors] || colors['Other']
    }

    const isContractExpiringSoon = () => {
        if (!vendor.contractEndDate) return false
        const endDate = new Date(vendor.contractEndDate)
        const today = new Date()
        const daysUntilExpiry = Math.ceil(
            (endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24),
        )
        return daysUntilExpiry <= 90 && daysUntilExpiry > 0
    }

    const isContractExpired = () => {
        if (!vendor.contractEndDate) return false
        return new Date(vendor.contractEndDate) < new Date()
    }

    const isAssessmentOverdue = () => {
        if (!vendor.nextAssessmentDate) return false
        return new Date(vendor.nextAssessmentDate) < new Date()
    }

    if (compact) {
        return (
            <div
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-colors border-gray-200 dark:border-gray-700"
                onClick={() => onView?.(vendor)}
            >
                <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                        <HiOutlineOfficeBuilding className="w-4 h-4 text-gray-500" />
                        {(isContractExpired() || isAssessmentOverdue()) && (
                            <HiOutlineExclamationCircle className="w-4 h-4 text-red-500" />
                        )}
                    </div>

                    <div className="flex-1 min-w-0">
                        <h4 className="font-medium truncate">{vendor.name}</h4>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                            <span>{vendor.category}</span>
                            {vendor.contractEndDate && (
                                <>
                                    <span>•</span>
                                    <span>
                                        Contract:{' '}
                                        {new Date(
                                            vendor.contractEndDate,
                                        ).toLocaleDateString()}
                                    </span>
                                </>
                            )}
                        </div>
                    </div>
                </div>

                <div className="flex items-center gap-2">
                    <StatusBadge status={vendor.status} size="sm" />
                    <StatusBadge status={vendor.riskLevel} size="sm" />
                </div>
            </div>
        )
    }

    return (
        <Card
            clickable
            className="h-full transition-all duration-200 hover:shadow-lg"
            bodyClass="h-full flex flex-col p-4"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={() => onView?.(vendor)}
        >
            {/* Header */}
            <div className="flex justify-between items-start mb-3">
                <div className="flex items-center gap-2">
                    <HiOutlineOfficeBuilding className="w-5 h-5 text-gray-500" />
                    <StatusBadge status={vendor.status} size="sm" />
                    {(isContractExpired() ||
                        isAssessmentOverdue() ||
                        isContractExpiringSoon()) && (
                        <HiOutlineExclamationCircle
                            className={`w-4 h-4 ${
                                isContractExpired() || isAssessmentOverdue()
                                    ? 'text-red-500'
                                    : 'text-amber-500'
                            }`}
                        />
                    )}
                </div>
                <StatusBadge status={vendor.riskLevel} size="sm" />
            </div>

            {/* Vendor Info */}
            <div className="flex-1">
                <h4 className="font-semibold mb-2 line-clamp-2">
                    {vendor.name}
                </h4>

                <div
                    className={`inline-block px-2 py-1 rounded-md text-xs font-medium border mb-3 ${getCategoryColor(vendor.category)}`}
                >
                    {vendor.category}
                </div>

                {vendor.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-3">
                        {vendor.description}
                    </p>
                )}

                {/* Contact Info */}
                <div className="space-y-2 text-sm mb-3">
                    {vendor.contactEmail && (
                        <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                            <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                            <span className="truncate">
                                {vendor.contactEmail}
                            </span>
                        </div>
                    )}
                    {vendor.website && (
                        <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                            <HiOutlineGlobe className="w-3 h-3" />
                            <span className="truncate">{vendor.website}</span>
                        </div>
                    )}
                </div>

                {/* Contract & Assessment Info */}
                <div className="space-y-2 text-xs">
                    {vendor.contractEndDate && (
                        <div
                            className={`flex justify-between ${
                                isContractExpired()
                                    ? 'text-red-600'
                                    : isContractExpiringSoon()
                                      ? 'text-amber-600'
                                      : 'text-gray-500'
                            }`}
                        >
                            <span>Contract End:</span>
                            <span>
                                {new Date(
                                    vendor.contractEndDate,
                                ).toLocaleDateString()}
                            </span>
                        </div>
                    )}
                    {vendor.nextAssessmentDate && (
                        <div
                            className={`flex justify-between ${
                                isAssessmentOverdue()
                                    ? 'text-red-600'
                                    : 'text-gray-500'
                            }`}
                        >
                            <span>Next Assessment:</span>
                            <span>
                                {new Date(
                                    vendor.nextAssessmentDate,
                                ).toLocaleDateString()}
                            </span>
                        </div>
                    )}
                </div>
            </div>

            {/* Meta Info */}
            <div className="flex justify-between items-center mb-3 text-xs text-gray-500">
                <div className="flex items-center gap-3">
                    <div className="flex items-center gap-1">
                        <HiOutlineDocumentText className="w-3 h-3" />
                        <span>{vendor.documents.length} docs</span>
                    </div>
                    <div className="flex items-center gap-1">
                        <HiOutlineCalendar className="w-3 h-3" />
                        <span>{vendor.controlIds.length} controls</span>
                    </div>
                </div>
            </div>

            {/* Footer */}
            <div className="flex justify-between items-center pt-3 border-t border-gray-200 dark:border-gray-700">
                <div className="text-xs text-gray-500">
                    Added: {new Date(vendor.createdAt).toLocaleDateString()}
                </div>

                {/* Action Buttons */}
                <div
                    className={`flex gap-1 transition-opacity duration-200 ${isHovered ? 'opacity-100' : 'opacity-0'}`}
                >
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineEye />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onView?.(vendor)
                        }}
                        title="View Details"
                    />
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineDocumentText />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onViewDocuments?.(vendor)
                        }}
                        title="View Documents"
                    />
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineShieldCheck />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onViewControls?.(vendor)
                        }}
                        title="View Controls"
                    />
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineCog />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onEdit?.(vendor)
                        }}
                        title="Settings"
                    />
                </div>
            </div>
        </Card>
    )
}

export default VendorCard
