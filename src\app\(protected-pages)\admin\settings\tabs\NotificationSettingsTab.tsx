'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import FormItem from '@/components/ui/Form/FormItem'
import FormContainer from '@/components/ui/Form/FormContainer'
import {
    HiOutlineBell,
    HiOutlineMail,
    HiOutlineDeviceMobile,
} from 'react-icons/hi'

const NotificationSettingsTab = () => {
    const [settings, setSettings] = useState({
        emailNotifications: {
            taskReminders: true,
            riskAlerts: true,
            complianceDeadlines: true,
            userActivity: false,
            systemUpdates: true,
            weeklyReports: true,
        },
        pushNotifications: {
            taskReminders: true,
            riskAlerts: true,
            complianceDeadlines: true,
            userActivity: false,
        },
        slackIntegration: {
            enabled: false,
            webhookUrl: '',
            channels: {
                alerts: '#compliance-alerts',
                reports: '#compliance-reports',
            },
        },
    })

    const [loading, setLoading] = useState(false)

    const handleSave = async () => {
        setLoading(true)
        try {
            // TODO: Save notification settings to backend
            console.log('Saving notification settings:', settings)
            await new Promise((resolve) => setTimeout(resolve, 1000)) // Mock delay
        } catch (error) {
            console.error('Error saving notification settings:', error)
        } finally {
            setLoading(false)
        }
    }

    const handleEmailToggle = (field: string) => {
        setSettings((prev) => ({
            ...prev,
            emailNotifications: {
                ...prev.emailNotifications,
                [field]:
                    !prev.emailNotifications[
                        field as keyof typeof prev.emailNotifications
                    ],
            },
        }))
    }

    const handlePushToggle = (field: string) => {
        setSettings((prev) => ({
            ...prev,
            pushNotifications: {
                ...prev.pushNotifications,
                [field]:
                    !prev.pushNotifications[
                        field as keyof typeof prev.pushNotifications
                    ],
            },
        }))
    }

    const handleSlackToggle = () => {
        setSettings((prev) => ({
            ...prev,
            slackIntegration: {
                ...prev.slackIntegration,
                enabled: !prev.slackIntegration.enabled,
            },
        }))
    }

    return (
        <div className="space-y-6">
            <div>
                <h3 className="text-lg font-semibold mb-2">
                    Notification Settings
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                    Configure how and when users receive notifications
                </p>
            </div>

            <Card>
                <div className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                        <HiOutlineMail className="w-5 h-5 text-blue-500" />
                        <h4 className="text-md font-semibold">
                            Email Notifications
                        </h4>
                    </div>
                    <FormContainer>
                        <div className="space-y-4">
                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            Task Reminders
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Send reminders for upcoming task
                                            deadlines
                                        </p>
                                    </div>
                                    <input
                                        type="checkbox"
                                        checked={
                                            settings.emailNotifications
                                                .taskReminders
                                        }
                                        onChange={() =>
                                            handleEmailToggle('taskReminders')
                                        }
                                        className="rounded border-gray-300"
                                    />
                                </label>
                            </FormItem>

                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            Risk Alerts
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Notify when new risks are identified
                                        </p>
                                    </div>
                                    <input
                                        type="checkbox"
                                        checked={
                                            settings.emailNotifications
                                                .riskAlerts
                                        }
                                        onChange={() =>
                                            handleEmailToggle('riskAlerts')
                                        }
                                        className="rounded border-gray-300"
                                    />
                                </label>
                            </FormItem>

                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            Compliance Deadlines
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Alert for approaching compliance
                                            deadlines
                                        </p>
                                    </div>
                                    <input
                                        type="checkbox"
                                        checked={
                                            settings.emailNotifications
                                                .complianceDeadlines
                                        }
                                        onChange={() =>
                                            handleEmailToggle(
                                                'complianceDeadlines',
                                            )
                                        }
                                        className="rounded border-gray-300"
                                    />
                                </label>
                            </FormItem>

                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            User Activity
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Notify about user logins and actions
                                        </p>
                                    </div>
                                    <input
                                        type="checkbox"
                                        checked={
                                            settings.emailNotifications
                                                .userActivity
                                        }
                                        onChange={() =>
                                            handleEmailToggle('userActivity')
                                        }
                                        className="rounded border-gray-300"
                                    />
                                </label>
                            </FormItem>

                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            System Updates
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Notify about system maintenance and
                                            updates
                                        </p>
                                    </div>
                                    <input
                                        type="checkbox"
                                        checked={
                                            settings.emailNotifications
                                                .systemUpdates
                                        }
                                        onChange={() =>
                                            handleEmailToggle('systemUpdates')
                                        }
                                        className="rounded border-gray-300"
                                    />
                                </label>
                            </FormItem>

                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            Weekly Reports
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Send weekly compliance summary
                                            reports
                                        </p>
                                    </div>
                                    <input
                                        type="checkbox"
                                        checked={
                                            settings.emailNotifications
                                                .weeklyReports
                                        }
                                        onChange={() =>
                                            handleEmailToggle('weeklyReports')
                                        }
                                        className="rounded border-gray-300"
                                    />
                                </label>
                            </FormItem>
                        </div>
                    </FormContainer>
                </div>
            </Card>

            <Card>
                <div className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                        <HiOutlineDeviceMobile className="w-5 h-5 text-green-500" />
                        <h4 className="text-md font-semibold">
                            Push Notifications
                        </h4>
                    </div>
                    <FormContainer>
                        <div className="space-y-4">
                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            Task Reminders
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Push notifications for task
                                            deadlines
                                        </p>
                                    </div>
                                    <input
                                        type="checkbox"
                                        checked={
                                            settings.pushNotifications
                                                .taskReminders
                                        }
                                        onChange={() =>
                                            handlePushToggle('taskReminders')
                                        }
                                        className="rounded border-gray-300"
                                    />
                                </label>
                            </FormItem>

                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            Risk Alerts
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Push notifications for new risks
                                        </p>
                                    </div>
                                    <input
                                        type="checkbox"
                                        checked={
                                            settings.pushNotifications
                                                .riskAlerts
                                        }
                                        onChange={() =>
                                            handlePushToggle('riskAlerts')
                                        }
                                        className="rounded border-gray-300"
                                    />
                                </label>
                            </FormItem>

                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            Compliance Deadlines
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Push notifications for deadlines
                                        </p>
                                    </div>
                                    <input
                                        type="checkbox"
                                        checked={
                                            settings.pushNotifications
                                                .complianceDeadlines
                                        }
                                        onChange={() =>
                                            handlePushToggle(
                                                'complianceDeadlines',
                                            )
                                        }
                                        className="rounded border-gray-300"
                                    />
                                </label>
                            </FormItem>

                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            User Activity
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Push notifications for user activity
                                        </p>
                                    </div>
                                    <input
                                        type="checkbox"
                                        checked={
                                            settings.pushNotifications
                                                .userActivity
                                        }
                                        onChange={() =>
                                            handlePushToggle('userActivity')
                                        }
                                        className="rounded border-gray-300"
                                    />
                                </label>
                            </FormItem>
                        </div>
                    </FormContainer>
                </div>
            </Card>

            <Card>
                <div className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                        <HiOutlineBell className="w-5 h-5 text-purple-500" />
                        <h4 className="text-md font-semibold">
                            Slack Integration
                        </h4>
                    </div>
                    <FormContainer>
                        <div className="space-y-4">
                            <FormItem>
                                <label className="flex items-center justify-between">
                                    <div>
                                        <span className="font-medium">
                                            Enable Slack Integration
                                        </span>
                                        <p className="text-sm text-gray-500">
                                            Send notifications to Slack channels
                                        </p>
                                    </div>
                                    <input
                                        type="checkbox"
                                        checked={
                                            settings.slackIntegration.enabled
                                        }
                                        onChange={handleSlackToggle}
                                        className="rounded border-gray-300"
                                    />
                                </label>
                            </FormItem>

                            {settings.slackIntegration.enabled && (
                                <>
                                    <FormItem label="Webhook URL">
                                        <input
                                            type="url"
                                            placeholder="https://hooks.slack.com/services/..."
                                            value={
                                                settings.slackIntegration
                                                    .webhookUrl
                                            }
                                            onChange={(e) =>
                                                setSettings((prev) => ({
                                                    ...prev,
                                                    slackIntegration: {
                                                        ...prev.slackIntegration,
                                                        webhookUrl:
                                                            e.target.value,
                                                    },
                                                }))
                                            }
                                            className="w-full px-3 py-2 border rounded-md"
                                        />
                                    </FormItem>

                                    <div className="grid grid-cols-2 gap-4">
                                        <FormItem label="Alerts Channel">
                                            <input
                                                type="text"
                                                placeholder="#compliance-alerts"
                                                value={
                                                    settings.slackIntegration
                                                        .channels.alerts
                                                }
                                                onChange={(e) =>
                                                    setSettings((prev) => ({
                                                        ...prev,
                                                        slackIntegration: {
                                                            ...prev.slackIntegration,
                                                            channels: {
                                                                ...prev
                                                                    .slackIntegration
                                                                    .channels,
                                                                alerts: e.target
                                                                    .value,
                                                            },
                                                        },
                                                    }))
                                                }
                                                className="w-full px-3 py-2 border rounded-md"
                                            />
                                        </FormItem>

                                        <FormItem label="Reports Channel">
                                            <input
                                                type="text"
                                                placeholder="#compliance-reports"
                                                value={
                                                    settings.slackIntegration
                                                        .channels.reports
                                                }
                                                onChange={(e) =>
                                                    setSettings((prev) => ({
                                                        ...prev,
                                                        slackIntegration: {
                                                            ...prev.slackIntegration,
                                                            channels: {
                                                                ...prev
                                                                    .slackIntegration
                                                                    .channels,
                                                                reports:
                                                                    e.target
                                                                        .value,
                                                            },
                                                        },
                                                    }))
                                                }
                                                className="w-full px-3 py-2 border rounded-md"
                                            />
                                        </FormItem>
                                    </div>
                                </>
                            )}
                        </div>
                    </FormContainer>
                </div>
            </Card>

            <div className="flex justify-end">
                <Button
                    variant="solid"
                    onClick={handleSave}
                    loading={loading}
                    className="bg-emerald-500 hover:bg-emerald-600"
                >
                    Save Notification Settings
                </Button>
            </div>
        </div>
    )
}

export default NotificationSettingsTab
