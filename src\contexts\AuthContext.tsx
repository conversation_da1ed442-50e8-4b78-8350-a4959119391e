'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import SupabaseAuthService from '@/services/SupabaseAuthService'
import appConfig from '@/configs/app.config'
import type { User, Session } from '@supabase/supabase-js'
import type { ReactNode } from 'react'

interface AuthContextType {
    user: User | null
    session: Session | null
    loading: boolean
    signIn: (
        email: string,
        password: string,
    ) => Promise<{ error: Error | null }>
    signUp: (
        email: string,
        password: string,
        userName?: string,
    ) => Promise<{ error: Error | null }>
    signInWithGoogle: () => Promise<void>
    signInWithGitHub: () => Promise<void>
    signOut: () => Promise<void>
    resetPassword: (email: string) => Promise<{ error: Error | null }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
    const context = useContext(AuthContext)
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider')
    }
    return context
}

interface AuthProviderProps {
    children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
    const [user, setUser] = useState<User | null>(null)
    const [session, setSession] = useState<Session | null>(null)
    const [loading, setLoading] = useState(true)
    const router = useRouter()

    useEffect(() => {
        // Get initial session
        SupabaseAuthService.getCurrentSession().then(({ session }) => {
            setSession(session)
            setUser(session?.user ?? null)
            setLoading(false)
        })

        // Listen for auth changes
        const {
            data: { subscription },
        } = SupabaseAuthService.onAuthStateChange(async (event, session) => {
            setSession(session)
            setUser(session?.user ?? null)
            setLoading(false)

            if (event === 'SIGNED_IN') {
                router.push(appConfig.authenticatedEntryPath)
            } else if (event === 'SIGNED_OUT') {
                router.push(appConfig.unAuthenticatedEntryPath)
            }
        })

        return () => subscription.unsubscribe()
    }, [router])

    const signIn = async (email: string, password: string) => {
        const { error } = await SupabaseAuthService.signInWithPassword({
            email,
            password,
        })
        return { error }
    }

    const signUp = async (
        email: string,
        password: string,
        userName?: string,
    ) => {
        const { error } = await SupabaseAuthService.signUpWithPassword({
            email,
            password,
            userName,
        })
        return { error }
    }

    const signInWithGoogle = async () => {
        await SupabaseAuthService.signInWithGoogle()
    }

    const signInWithGitHub = async () => {
        await SupabaseAuthService.signInWithGitHub()
    }

    const signOut = async () => {
        await SupabaseAuthService.signOut()
    }

    const resetPassword = async (email: string) => {
        const { error } = await SupabaseAuthService.resetPassword(email)
        return { error }
    }

    const value = {
        user,
        session,
        loading,
        signIn,
        signUp,
        signInWithGoogle,
        signInWithGitHub,
        signOut,
        resetPassword,
    }

    return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
