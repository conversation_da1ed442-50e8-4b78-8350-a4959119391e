'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import StatusBadge from '../components/StatusBadge'
import Radio from '@/components/ui/Radio'
import { PiCheckCircleDuotone, PiArrowRightDuotone } from 'react-icons/pi'

const MySubscriptionTab = () => {
    const [selectedPlan, setSelectedPlan] = useState('growth')

    return (
        <div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <div className="lg:col-span-2">
                    <h5 className="font-bold text-lg mb-4">Choose Your Plan</h5>

                    {/* Plan Selection */}
                    <div className="space-y-4">
                        {plans.map((plan) => (
                            <Card
                                key={plan.id}
                                className={`border-2 ${selectedPlan === plan.id ? 'border-primary' : 'border-gray-200 dark:border-gray-700'}`}
                                clickable
                                onClick={() => setSelectedPlan(plan.id)}
                            >
                                <div className="flex items-start">
                                    <Radio
                                        checked={selectedPlan === plan.id}
                                        onChange={() =>
                                            setSelectedPlan(plan.id)
                                        }
                                        className="mt-1 mr-3"
                                    />
                                    <div className="flex-grow">
                                        <div className="flex flex-col md:flex-row md:items-center justify-between mb-2">
                                            <div className="flex items-center mb-2 md:mb-0">
                                                <h6 className="font-semibold text-lg mr-2">
                                                    {plan.name}
                                                </h6>

                                                {plan.popular && (
                                                    <StatusBadge className="bg-amber-100 text-amber-600 border border-amber-200 ml-2 text-xs">
                                                        Popular
                                                    </StatusBadge>
                                                )}
                                            </div>
                                            <div className="text-right">
                                                <div className="text-xl font-bold">
                                                    {plan.price}
                                                </div>
                                                <div className="text-xs text-gray-500">
                                                    per month
                                                </div>
                                            </div>
                                        </div>
                                        <p className="text-sm text-gray-500 mb-3">
                                            {plan.description}
                                        </p>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <h6 className="font-medium mb-2">
                                                    Features
                                                </h6>
                                                <ul className="text-sm space-y-2">
                                                    {plan.features.map(
                                                        (feature, index) => (
                                                            <li
                                                                key={index}
                                                                className="flex items-start"
                                                            >
                                                                <span className="text-emerald-500 mr-2 mt-0.5">
                                                                    <PiCheckCircleDuotone />
                                                                </span>
                                                                <span>
                                                                    {feature}
                                                                </span>
                                                            </li>
                                                        ),
                                                    )}
                                                </ul>
                                            </div>

                                            <div>
                                                <h6 className="font-medium mb-2">
                                                    Add-Ons
                                                </h6>
                                                <ul className="text-sm space-y-2">
                                                    {plan.addOns.map(
                                                        (addon, index) => (
                                                            <li
                                                                key={index}
                                                                className="flex items-start"
                                                            >
                                                                <span className="text-emerald-500 mr-2 mt-0.5">
                                                                    <PiCheckCircleDuotone />
                                                                </span>
                                                                <span>
                                                                    {addon}
                                                                </span>
                                                            </li>
                                                        ),
                                                    )}
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </Card>
                        ))}
                    </div>
                </div>

                {/* Order Summary */}
                <div>
                    <Card>
                        <h5 className="font-bold text-lg mb-4">
                            Order Summary
                        </h5>
                        <div className="space-y-3 mb-4">
                            <div className="flex justify-between">
                                <span className="text-gray-500">Base Plan</span>
                                <span className="font-medium">
                                    {
                                        plans.find((p) => p.id === selectedPlan)
                                            ?.price
                                    }
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-gray-500">Add-ons</span>
                                <span className="font-medium">Included</span>
                            </div>
                            <div className="border-t border-gray-200 dark:border-gray-700 pt-3 flex justify-between">
                                <span className="font-medium">Total</span>
                                <span className="font-bold">
                                    {
                                        plans.find((p) => p.id === selectedPlan)
                                            ?.price
                                    }
                                </span>
                            </div>
                        </div>

                        <div className="space-y-3">
                            <Button
                                block
                                variant="solid"
                                className="bg-emerald-500 hover:bg-emerald-600"
                                icon={<PiArrowRightDuotone />}
                            >
                                Continue
                            </Button>
                            <p className="text-xs text-gray-500 text-center">
                                By continuing, you agree to our Terms of Service
                                and Privacy Policy.
                            </p>
                        </div>
                    </Card>
                </div>
            </div>
        </div>
    )
}

// Sample data
const plans = [
    {
        id: 'starter',
        name: 'Starter',
        price: '£49/mo',
        description:
            'Perfect for small teams or individuals just getting started with compliance.',
        badgeColor: 'bg-blue-100 text-blue-600 border border-blue-200',
        popular: false,
        features: ['1 user', '1 framework', 'Basic tasks', 'Email support'],
        addOns: ['Basic tasks'],
    },
    {
        id: 'growth',
        name: 'Growth',
        price: '£199/mo',
        description:
            'Ideal for growing organizations with multiple compliance requirements.',
        badgeColor: 'bg-emerald-100 text-emerald-600 border border-emerald-200',
        popular: true,
        features: [
            '5 users',
            '3 frameworks',
            'Advanced reporting',
            'Priority support',
        ],
        addOns: ['Risk management', 'Basic tasks'],
    },
    {
        id: 'enterprise',
        name: 'Enterprise',
        price: 'Custom',
        description:
            'For large organizations with complex compliance needs and custom requirements.',
        badgeColor: 'bg-purple-100 text-purple-600 border border-purple-200',
        popular: false,
        features: [
            'Unlimited users',
            'Unlimited frameworks',
            'Custom integrations',
            'Dedicated support',
        ],
        addOns: ['All add-ons included', 'Custom add-ons available'],
    },
]

export default MySubscriptionTab
