-- Compliance Platform Database Schema
-- This file contains the SQL schema for the modular compliance platform

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Organizations table
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255),
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Frameworks table
CREATE TABLE frameworks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    version VARCHAR(50),
    category VARCHAR(100) NOT NULL,
    description TEXT,
    region VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'Not Started',
    control_count INTEGER DEFAULT 0,
    progress INTEGER DEFAULT 0,
    last_updated DATE,
    notifications INTEGER DEFAULT 0,
    created_by <PERSON><PERSON><PERSON>,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Controls table
CREATE TABLE controls (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    framework_id UUID NOT NULL REFERENCES frameworks(id) ON DELETE CASCADE,
    control_number VARCHAR(50) NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    subcategory VARCHAR(100),
    requirements TEXT[],
    status VARCHAR(50) NOT NULL DEFAULT 'Not Started',
    evidence_readiness VARCHAR(50) NOT NULL DEFAULT 'Not Ready',
    owner_id UUID,
    assignee_id UUID,
    due_date DATE,
    last_review_date DATE,
    next_review_date DATE,
    notes TEXT,
    tags TEXT[],
    risk_level VARCHAR(20) NOT NULL DEFAULT 'Medium',
    implementation_effort VARCHAR(20) NOT NULL DEFAULT 'Medium',
    business_impact VARCHAR(20) NOT NULL DEFAULT 'Medium',
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tasks table
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(500) NOT NULL,
    description TEXT,
    type VARCHAR(20) NOT NULL DEFAULT 'One-time',
    frequency VARCHAR(20),
    status VARCHAR(50) NOT NULL DEFAULT 'Open',
    priority VARCHAR(20) NOT NULL DEFAULT 'Medium',
    assignee_id UUID,
    owner_id UUID,
    due_date DATE NOT NULL,
    completed_date DATE,
    estimated_hours INTEGER,
    actual_hours INTEGER,
    control_ids UUID[],
    framework_ids UUID[],
    tags TEXT[],
    attachments TEXT[],
    recurrence_rule JSONB,
    parent_task_id UUID REFERENCES tasks(id),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Task comments table
CREATE TABLE task_comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    author_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Risks table
CREATE TABLE risks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(500) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    likelihood INTEGER NOT NULL CHECK (likelihood >= 1 AND likelihood <= 5),
    impact INTEGER NOT NULL CHECK (impact >= 1 AND impact <= 5),
    risk_score INTEGER GENERATED ALWAYS AS (likelihood * impact) STORED,
    inherent_risk INTEGER,
    residual_risk INTEGER,
    status VARCHAR(50) NOT NULL DEFAULT 'Identified',
    owner_id UUID,
    control_ids UUID[],
    mitigation_plan TEXT,
    mitigation_deadline DATE,
    last_review_date DATE,
    next_review_date DATE,
    tags TEXT[],
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Vendors table
CREATE TABLE vendors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    website VARCHAR(255),
    description TEXT,
    category VARCHAR(100) NOT NULL,
    risk_level VARCHAR(20) NOT NULL DEFAULT 'Medium',
    status VARCHAR(50) NOT NULL DEFAULT 'Active',
    contract_start_date DATE,
    contract_end_date DATE,
    last_assessment_date DATE,
    next_assessment_date DATE,
    control_ids UUID[],
    tags TEXT[],
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Vendor documents table
CREATE TABLE vendor_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    url TEXT,
    expiry_date DATE,
    status VARCHAR(50) NOT NULL DEFAULT 'Current',
    uploaded_by UUID NOT NULL,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Policies table
CREATE TABLE policies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(500) NOT NULL,
    description TEXT,
    content TEXT NOT NULL,
    version VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'Draft',
    category VARCHAR(100),
    owner_id UUID,
    approver_id UUID,
    effective_date DATE,
    review_date DATE,
    next_review_date DATE,
    control_ids UUID[],
    tags TEXT[],
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Policy acknowledgments table
CREATE TABLE policy_acknowledgments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    policy_id UUID NOT NULL REFERENCES policies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    acknowledged_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    version VARCHAR(50) NOT NULL
);

-- Assets table
CREATE TABLE assets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(100) NOT NULL,
    classification VARCHAR(50) NOT NULL DEFAULT 'Internal',
    owner_id UUID,
    custodian_id UUID,
    location VARCHAR(255),
    status VARCHAR(50) NOT NULL DEFAULT 'Active',
    last_review_date DATE,
    next_review_date DATE,
    control_ids UUID[],
    risk_ids UUID[],
    tags TEXT[],
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Evidence table
CREATE TABLE evidence (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(500) NOT NULL,
    description TEXT,
    type VARCHAR(100) NOT NULL,
    url TEXT,
    file_name VARCHAR(255),
    file_size BIGINT,
    mime_type VARCHAR(100),
    status VARCHAR(50) NOT NULL DEFAULT 'Pending',
    control_ids UUID[],
    task_ids UUID[],
    vendor_ids UUID[],
    collected_date DATE NOT NULL,
    expiry_date DATE,
    reviewed_by UUID,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    tags TEXT[],
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_frameworks_organization_id ON frameworks(organization_id);
CREATE INDEX idx_frameworks_status ON frameworks(status);
CREATE INDEX idx_controls_framework_id ON controls(framework_id);
CREATE INDEX idx_controls_organization_id ON controls(organization_id);
CREATE INDEX idx_controls_status ON controls(status);
CREATE INDEX idx_tasks_organization_id ON tasks(organization_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);
CREATE INDEX idx_tasks_assignee_id ON tasks(assignee_id);
CREATE INDEX idx_risks_organization_id ON risks(organization_id);
CREATE INDEX idx_risks_risk_score ON risks(risk_score);
CREATE INDEX idx_vendors_organization_id ON vendors(organization_id);
CREATE INDEX idx_policies_organization_id ON policies(organization_id);
CREATE INDEX idx_assets_organization_id ON assets(organization_id);
CREATE INDEX idx_evidence_organization_id ON evidence(organization_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to all tables
CREATE TRIGGER update_frameworks_updated_at BEFORE UPDATE ON frameworks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_controls_updated_at BEFORE UPDATE ON controls FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_risks_updated_at BEFORE UPDATE ON risks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vendors_updated_at BEFORE UPDATE ON vendors FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_policies_updated_at BEFORE UPDATE ON policies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assets_updated_at BEFORE UPDATE ON assets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_evidence_updated_at BEFORE UPDATE ON evidence FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE frameworks ENABLE ROW LEVEL SECURITY;
ALTER TABLE controls ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE risks ENABLE ROW LEVEL SECURITY;
ALTER TABLE vendors ENABLE ROW LEVEL SECURITY;
ALTER TABLE policies ENABLE ROW LEVEL SECURITY;
ALTER TABLE assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE evidence ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (basic organization-based access)
-- Note: You'll need to customize these based on your auth setup

CREATE POLICY "Users can view their organization's frameworks" ON frameworks
    FOR SELECT USING (organization_id = current_setting('app.current_organization_id')::UUID);

CREATE POLICY "Users can view their organization's controls" ON controls
    FOR SELECT USING (organization_id = current_setting('app.current_organization_id')::UUID);

CREATE POLICY "Users can view their organization's tasks" ON tasks
    FOR SELECT USING (organization_id = current_setting('app.current_organization_id')::UUID);

CREATE POLICY "Users can view their organization's risks" ON risks
    FOR SELECT USING (organization_id = current_setting('app.current_organization_id')::UUID);

CREATE POLICY "Users can view their organization's vendors" ON vendors
    FOR SELECT USING (organization_id = current_setting('app.current_organization_id')::UUID);

CREATE POLICY "Users can view their organization's policies" ON policies
    FOR SELECT USING (organization_id = current_setting('app.current_organization_id')::UUID);

CREATE POLICY "Users can view their organization's assets" ON assets
    FOR SELECT USING (organization_id = current_setting('app.current_organization_id')::UUID);

CREATE POLICY "Users can view their organization's evidence" ON evidence
    FOR SELECT USING (organization_id = current_setting('app.current_organization_id')::UUID);
