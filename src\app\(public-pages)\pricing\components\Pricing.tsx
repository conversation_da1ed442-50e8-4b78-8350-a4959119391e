'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import LandingFooter from '../../landing/components/LandingFooter'
import Button from '@/components/ui/Button'
import { PiCheckDuotone, PiXDuotone, PiArrowLeftDuotone } from 'react-icons/pi'
import { MODE_DARK, MODE_LIGHT } from '@/constants/theme.constant'
import { useRouter } from 'next/navigation'
import type { Mode } from '@/@types/theme'

const Pricing = () => {
    const [mode, setMode] = useState<Mode>(MODE_LIGHT)
    const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>(
        'monthly',
    )
    const router = useRouter()

    const toggleMode = () => {
        setMode(mode === MODE_LIGHT ? MODE_DARK : MODE_LIGHT)
    }

    const plans = [
        {
            name: 'Starter',
            description:
                'Perfect for small teams getting started with compliance',
            monthlyPrice: 29,
            yearlyPrice: 290,
            features: [
                'Up to 5 team members',
                '3 compliance frameworks',
                'Basic gap analysis',
                'Standard templates',
                'Email support',
                'Basic reporting',
            ],
            limitations: [
                'Limited integrations',
                'No custom workflows',
                'No API access',
            ],
            popular: false,
            cta: 'Get Started Now',
        },
        {
            name: 'Professional',
            description:
                'Ideal for growing organizations with advanced compliance needs',
            monthlyPrice: 79,
            yearlyPrice: 790,
            features: [
                'Up to 25 team members',
                'Unlimited frameworks',
                'Advanced gap analysis',
                'Custom templates',
                'Priority support',
                'Advanced reporting',
                'Workflow automation',
                'API access',
                'Custom integrations',
                'Audit trail',
            ],
            limitations: ['Limited white-labeling'],
            popular: true,
            cta: 'Get Started Now',
        },
        {
            name: 'Enterprise',
            description:
                'For large organizations requiring maximum flexibility and control',
            monthlyPrice: 199,
            yearlyPrice: 1990,
            features: [
                'Unlimited team members',
                'All compliance frameworks',
                'AI-powered analysis',
                'White-label solution',
                'Dedicated support',
                'Custom reporting',
                'Advanced workflows',
                'Full API access',
                'SSO integration',
                'Custom deployment',
                'Training & onboarding',
                'SLA guarantee',
            ],
            limitations: [],
            popular: false,
            cta: 'Contact Sales',
        },
    ]

    const handleGetStarted = (planName: string) => {
        if (planName === 'Enterprise') {
            // Handle contact sales
            window.open(
                'mailto:<EMAIL>?subject=Enterprise Plan Inquiry',
                '_blank',
            )
        } else {
            // Always redirect to sign up for all plans
            router.push('/sign-up')
        }
    }

    return (
        <main className={`min-h-screen ${mode === MODE_DARK ? 'dark' : ''}`}>
            <div className="bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
                {/* Header with go back button and theme toggle */}
                <div className="pt-8 px-4">
                    <div className="max-w-7xl mx-auto flex justify-between items-center">
                        <Link
                            href="/"
                            className="inline-flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-colors"
                        >
                            <PiArrowLeftDuotone className="text-lg" />
                            Go back
                        </Link>

                        {/* Theme toggle button */}
                        <button
                            className="relative flex cursor-pointer items-center justify-center rounded-xl p-2 text-neutral-500 hover:shadow-input dark:text-neutral-500"
                            onClick={toggleMode}
                        >
                            <svg
                                className="lucide lucide-sun rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"
                                fill="none"
                                height="16"
                                stroke="currentColor"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                viewBox="0 0 24 24"
                                width="16"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <circle cx="12" cy="12" r="4" />
                                <path d="M12 2v2" />
                                <path d="M12 20v2" />
                                <path d="m4.93 4.93 1.41 1.41" />
                                <path d="m17.66 17.66 1.41 1.41" />
                                <path d="M2 12h2" />
                                <path d="M20 12h2" />
                                <path d="m6.34 17.66-1.41 1.41" />
                                <path d="m19.07 4.93-1.41 1.41" />
                            </svg>
                            <svg
                                className="lucide lucide-moon absolute rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"
                                fill="none"
                                height="16"
                                stroke="currentColor"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                viewBox="0 0 24 24"
                                width="16"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z" />
                            </svg>
                            <span className="sr-only">Toggle theme</span>
                        </button>
                    </div>
                </div>

                {/* Hero Section */}
                <div className="pt-16 pb-16 px-4">
                    <div className="max-w-7xl mx-auto text-center">
                        <motion.h1
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                            className="text-4xl md:text-6xl font-bold mb-6"
                        >
                            Simple, Transparent Pricing
                        </motion.h1>
                        <motion.p
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.1 }}
                            className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto"
                        >
                            Choose the perfect plan for your compliance needs.
                            All plans include a 14-day free trial.
                        </motion.p>

                        {/* Billing Toggle */}
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.2 }}
                            className="flex items-center justify-center gap-4 mb-16"
                        >
                            <span
                                className={`${billingCycle === 'monthly' ? 'text-gray-900 dark:text-white' : 'text-gray-500'}`}
                            >
                                Monthly
                            </span>
                            <button
                                onClick={() =>
                                    setBillingCycle(
                                        billingCycle === 'monthly'
                                            ? 'yearly'
                                            : 'monthly',
                                    )
                                }
                                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                                    billingCycle === 'yearly'
                                        ? 'bg-primary'
                                        : 'bg-gray-200 dark:bg-gray-700'
                                }`}
                            >
                                <span
                                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                        billingCycle === 'yearly'
                                            ? 'translate-x-6'
                                            : 'translate-x-1'
                                    }`}
                                />
                            </button>
                            <span
                                className={`${billingCycle === 'yearly' ? 'text-gray-900 dark:text-white' : 'text-gray-500'}`}
                            >
                                Yearly
                            </span>
                            {billingCycle === 'yearly' && (
                                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full text-sm font-medium">
                                    Save 17%
                                </span>
                            )}
                        </motion.div>
                    </div>
                </div>

                {/* Pricing Cards */}
                <div className="pb-16 px-4">
                    <div className="max-w-7xl mx-auto">
                        <div className="grid md:grid-cols-3 gap-8">
                            {plans.map((plan, index) => (
                                <motion.div
                                    key={plan.name}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{
                                        duration: 0.6,
                                        delay: 0.1 * index,
                                    }}
                                    className={`relative rounded-2xl p-8 ${
                                        plan.popular
                                            ? 'bg-primary text-white shadow-2xl scale-105'
                                            : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg'
                                    }`}
                                >
                                    {plan.popular && (
                                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                            <span className="bg-white text-primary px-4 py-2 rounded-full text-sm font-bold">
                                                Most Popular
                                            </span>
                                        </div>
                                    )}

                                    <div className="text-center mb-8">
                                        <h3 className="text-2xl font-bold mb-2">
                                            {plan.name}
                                        </h3>
                                        <p
                                            className={`text-sm mb-4 ${plan.popular ? 'text-blue-100' : 'text-gray-600 dark:text-gray-300'}`}
                                        >
                                            {plan.description}
                                        </p>
                                        <div className="mb-4">
                                            <span className="text-4xl font-bold">
                                                $
                                                {billingCycle === 'monthly'
                                                    ? plan.monthlyPrice
                                                    : Math.floor(
                                                          plan.yearlyPrice / 12,
                                                      )}
                                            </span>
                                            <span
                                                className={`text-sm ${plan.popular ? 'text-blue-100' : 'text-gray-500'}`}
                                            >
                                                /month
                                            </span>
                                        </div>
                                        {billingCycle === 'yearly' && (
                                            <p
                                                className={`text-sm ${plan.popular ? 'text-blue-100' : 'text-gray-500'}`}
                                            >
                                                Billed annually ($
                                                {plan.yearlyPrice}/year)
                                            </p>
                                        )}
                                    </div>

                                    <Button
                                        variant={
                                            plan.popular ? 'solid' : 'default'
                                        }
                                        className={`w-full mb-8 ${plan.popular ? 'bg-white text-primary hover:bg-gray-100' : ''}`}
                                        onClick={() =>
                                            handleGetStarted(plan.name)
                                        }
                                    >
                                        {plan.cta}
                                    </Button>

                                    <div className="space-y-4">
                                        <h4 className="font-semibold">
                                            What&apos;s included:
                                        </h4>
                                        <ul className="space-y-3">
                                            {plan.features.map(
                                                (feature, featureIndex) => (
                                                    <li
                                                        key={featureIndex}
                                                        className="flex items-start gap-3"
                                                    >
                                                        <PiCheckDuotone
                                                            className={`text-lg mt-0.5 flex-shrink-0 ${
                                                                plan.popular
                                                                    ? 'text-white'
                                                                    : 'text-green-500'
                                                            }`}
                                                        />
                                                        <span className="text-sm">
                                                            {feature}
                                                        </span>
                                                    </li>
                                                ),
                                            )}
                                        </ul>

                                        {plan.limitations.length > 0 && (
                                            <>
                                                <h4 className="font-semibold pt-4">
                                                    Not included:
                                                </h4>
                                                <ul className="space-y-3">
                                                    {plan.limitations.map(
                                                        (
                                                            limitation,
                                                            limitationIndex,
                                                        ) => (
                                                            <li
                                                                key={
                                                                    limitationIndex
                                                                }
                                                                className="flex items-start gap-3"
                                                            >
                                                                <PiXDuotone
                                                                    className={`text-lg mt-0.5 flex-shrink-0 ${
                                                                        plan.popular
                                                                            ? 'text-blue-200'
                                                                            : 'text-gray-400'
                                                                    }`}
                                                                />
                                                                <span
                                                                    className={`text-sm ${
                                                                        plan.popular
                                                                            ? 'text-blue-100'
                                                                            : 'text-gray-500'
                                                                    }`}
                                                                >
                                                                    {limitation}
                                                                </span>
                                                            </li>
                                                        ),
                                                    )}
                                                </ul>
                                            </>
                                        )}
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </div>

                {/* FAQ Section */}
                <div className="py-24 px-4 bg-gray-50 dark:bg-gray-800">
                    <div className="max-w-4xl mx-auto">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                            className="text-center mb-16"
                        >
                            <h2 className="text-3xl md:text-4xl font-bold mb-4">
                                Frequently Asked Questions
                            </h2>
                            <p className="text-gray-600 dark:text-gray-300">
                                Everything you need to know about our pricing
                                and plans
                            </p>
                        </motion.div>

                        <div className="space-y-8">
                            {[
                                {
                                    question:
                                        'Can I change my plan at any time?',
                                    answer: "Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, and we'll prorate any billing differences.",
                                },
                                {
                                    question:
                                        'What happens during the free trial?',
                                    answer: 'You get full access to all features of your chosen plan for 14 days. No credit card required. You can cancel anytime during the trial period.',
                                },
                                {
                                    question:
                                        'Do you offer discounts for annual billing?',
                                    answer: "Yes! Annual billing saves you 17% compared to monthly billing. You'll see the discounted price when you toggle to yearly billing above.",
                                },
                                {
                                    question:
                                        'What kind of support do you provide?',
                                    answer: 'Starter plans include email support, Professional plans get priority support, and Enterprise customers receive dedicated support with SLA guarantees.',
                                },
                                {
                                    question:
                                        'Can I add more team members later?',
                                    answer: 'Absolutely! You can add team members at any time. Additional users are billed pro-rata for the current billing period.',
                                },
                                {
                                    question: 'Is my data secure?',
                                    answer: 'Yes, we use enterprise-grade security with SOC 2 compliance, end-to-end encryption, and regular security audits. Your data is always protected.',
                                },
                            ].map((faq, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{
                                        duration: 0.6,
                                        delay: 0.1 * index,
                                    }}
                                    className="bg-white dark:bg-gray-700 rounded-xl p-6 shadow-sm"
                                >
                                    <h3 className="text-lg font-semibold mb-3">
                                        {faq.question}
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {faq.answer}
                                    </p>
                                </motion.div>
                            ))}
                        </div>

                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.8 }}
                            className="text-center mt-16"
                        >
                            <p className="text-gray-600 dark:text-gray-300 mb-4">
                                Still have questions? We&apos;re here to help.
                            </p>
                            <Button
                                variant="default"
                                onClick={() =>
                                    window.open(
                                        'mailto:<EMAIL>',
                                        '_blank',
                                    )
                                }
                            >
                                Contact Support
                            </Button>
                        </motion.div>
                    </div>
                </div>

                <LandingFooter mode={mode} />
            </div>
        </main>
    )
}

export default Pricing
