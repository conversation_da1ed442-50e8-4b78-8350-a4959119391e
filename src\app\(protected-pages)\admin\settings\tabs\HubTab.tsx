'use client'

import { useState } from 'react'
import Tabs from '@/components/ui/Tabs'
import {
    PiHouseLineDuotone,
    PiUserCircleDuotone,
    PiPuzzlePieceDuotone,
    PiCreditCardDuotone,
    PiRoadHorizonDuotone,
    PiStackDuotone,
} from 'react-icons/pi'

// Import the original hub tab components
import OverviewTab from '../../../hub/tabs/OverviewTab'
import FrameworksTab from '../../../hub/tabs/FrameworksTab'
import MySubscriptionTab from '../../../hub/tabs/MySubscriptionTab'
import AddOnsTab from '../../../hub/tabs/AddOnsTab'
import BillingUsageTab from '../../../hub/tabs/BillingUsageTab'
import RoadmapTab from '../../../hub/tabs/RoadmapTab'

const HubTab = () => {
    const [activeTab, setActiveTab] = useState('overview')

    return (
        <div>
            <div className="mb-6">
                <h3 className="text-lg font-semibold mb-2">
                    Hub & Billing Management
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                    Manage frameworks, subscriptions, billing, and
                    organizational settings
                </p>
            </div>

            <Tabs
                value={activeTab}
                onChange={(val) => setActiveTab(val as string)}
            >
                <Tabs.TabList>
                    <Tabs.TabNav value="overview" icon={<PiHouseLineDuotone />}>
                        Overview
                    </Tabs.TabNav>
                    <Tabs.TabNav value="frameworks" icon={<PiStackDuotone />}>
                        Frameworks
                    </Tabs.TabNav>
                    <Tabs.TabNav
                        value="subscription"
                        icon={<PiUserCircleDuotone />}
                    >
                        My Subscription
                    </Tabs.TabNav>
                    <Tabs.TabNav value="addons" icon={<PiPuzzlePieceDuotone />}>
                        Add-Ons
                    </Tabs.TabNav>
                    <Tabs.TabNav value="billing" icon={<PiCreditCardDuotone />}>
                        Billing & Usage
                    </Tabs.TabNav>
                    <Tabs.TabNav
                        value="roadmap"
                        icon={<PiRoadHorizonDuotone />}
                    >
                        Roadmap
                    </Tabs.TabNav>
                </Tabs.TabList>
                <div className="mt-4">
                    <Tabs.TabContent value="overview">
                        <OverviewTab />
                    </Tabs.TabContent>
                    <Tabs.TabContent value="frameworks">
                        <FrameworksTab />
                    </Tabs.TabContent>
                    <Tabs.TabContent value="subscription">
                        <MySubscriptionTab />
                    </Tabs.TabContent>
                    <Tabs.TabContent value="addons">
                        <AddOnsTab />
                    </Tabs.TabContent>
                    <Tabs.TabContent value="billing">
                        <BillingUsageTab />
                    </Tabs.TabContent>
                    <Tabs.TabContent value="roadmap">
                        <RoadmapTab />
                    </Tabs.TabContent>
                </div>
            </Tabs>
        </div>
    )
}

export default HubTab
