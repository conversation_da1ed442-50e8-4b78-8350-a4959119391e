'use client'

import { useState, useMemo } from 'react'
import { FilterBar } from '@/shared/components'
import { TaskFilter } from '../types'
import {
    TASK_STATUSES,
    PRIORITY_LEVELS,
    TASK_TYPES,
} from '@/shared/constants/compliance'
import TaskCard from './TaskCard'
import { TaskDetail } from '../types'
import Button from '@/components/ui/Button'
import Tabs from '@/components/ui/Tabs'
import {
    HiPlus,
    HiOutlineCalendar,
    HiOutlineViewGrid,
    HiOutlineViewList,
} from 'react-icons/hi'
import { isTaskOverdue } from '@/shared/utils/compliance'

interface TaskListProps {
    tasks: TaskDetail[]
    loading?: boolean
    onView?: (task: TaskDetail) => void
    onEdit?: (task: TaskDetail) => void
    onStart?: (task: TaskDetail) => void
    onComplete?: (task: TaskDetail) => void
    onComment?: (task: TaskDetail) => void
    onAddTask?: () => void
    onExportCSV?: () => void
}

const TaskList = ({
    tasks,
    loading = false,
    onView,
    onEdit,
    onStart,
    onComplete,
    onComment,
    onAddTask,
    onExportCSV,
}: TaskListProps) => {
    const [filters, setFilters] = useState<TaskFilter>({})
    const [activeTab, setActiveTab] = useState('all')
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

    // Filter options for the FilterBar
    const filterOptions = {
        status: TASK_STATUSES.map((status) => ({
            value: status,
            label: status,
        })),
        priority: PRIORITY_LEVELS.map((priority) => ({
            value: priority,
            label: priority,
        })),
        type: TASK_TYPES.map((type) => ({ value: type, label: type })),
        assignee: [
            { value: 'john-doe', label: 'John Doe' },
            { value: 'jane-smith', label: 'Jane Smith' },
            { value: 'mike-johnson', label: 'Mike Johnson' },
        ],
    }

    // Filter tasks based on current filters and active tab
    const filteredTasks = useMemo(() => {
        let filtered = tasks

        // Apply tab filter first
        switch (activeTab) {
            case 'my-tasks':
                filtered = filtered.filter(
                    (task) => task.assignee === 'current-user-id',
                ) // Replace with actual user ID
                break
            case 'overdue':
                filtered = filtered.filter((task) => isTaskOverdue(task))
                break
            case 'recurring':
                filtered = filtered.filter((task) => task.type === 'Recurring')
                break
            case 'completed':
                filtered = filtered.filter(
                    (task) => task.status === 'Completed',
                )
                break
            default:
                // 'all' - no additional filtering
                break
        }

        // Apply search and other filters
        return filtered.filter((task) => {
            // Search filter
            if (filters.search) {
                const searchTerm = filters.search.toLowerCase()
                const matchesSearch =
                    task.title.toLowerCase().includes(searchTerm) ||
                    (task.description &&
                        task.description.toLowerCase().includes(searchTerm)) ||
                    task.tags.some((tag) =>
                        tag.toLowerCase().includes(searchTerm),
                    )

                if (!matchesSearch) return false
            }

            // Status filter
            if (filters.status && filters.status.length > 0) {
                if (!filters.status.includes(task.status)) return false
            }

            // Priority filter
            if (filters.priority && filters.priority.length > 0) {
                if (!filters.priority.includes(task.priority)) return false
            }

            // Type filter
            if (filters.type && filters.type.length > 0) {
                if (!filters.type.includes(task.type)) return false
            }

            // Assignee filter
            if (filters.assignee && filters.assignee.length > 0) {
                if (!task.assignee || !filters.assignee.includes(task.assignee))
                    return false
            }

            return true
        })
    }, [tasks, filters, activeTab])

    // Calculate stats
    const stats = useMemo(() => {
        const overdueTasks = tasks.filter(isTaskOverdue)
        const myTasks = tasks.filter(
            (task) => task.assignee === 'current-user-id',
        )
        const recurringTasks = tasks.filter((task) => task.type === 'Recurring')

        return {
            total: tasks.length,
            overdue: overdueTasks.length,
            myTasks: myTasks.length,
            recurring: recurringTasks.length,
            completed: tasks.filter((task) => task.status === 'Completed')
                .length,
        }
    }, [tasks])

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="animate-pulse">
                    <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4"></div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {Array.from({ length: 6 }).map((_, i) => (
                            <div
                                key={i}
                                className="h-48 bg-gray-200 dark:bg-gray-700 rounded-lg"
                            ></div>
                        ))}
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 className="text-2xl font-bold">Task Engine</h1>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">
                        Manage recurring security and compliance tasks
                    </p>
                </div>
                <div className="flex gap-2">
                    <Button
                        variant="default"
                        icon={<HiOutlineCalendar />}
                        onClick={onExportCSV}
                    >
                        Export CSV
                    </Button>
                    <Button
                        variant="solid"
                        icon={<HiPlus />}
                        onClick={onAddTask}
                        className="bg-emerald-500 hover:bg-emerald-600"
                    >
                        Add Task
                    </Button>
                </div>
            </div>

            {/* Tabs */}
            <Tabs
                value={activeTab}
                onChange={(val) => setActiveTab(val as string)}
            >
                <Tabs.TabList>
                    <Tabs.TabNav value="all">
                        All Tasks ({stats.total})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="my-tasks">
                        My Tasks ({stats.myTasks})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="overdue">
                        Overdue ({stats.overdue})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="recurring">
                        Recurring ({stats.recurring})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="completed">
                        Completed ({stats.completed})
                    </Tabs.TabNav>
                </Tabs.TabList>
            </Tabs>

            {/* Filters and View Toggle */}
            <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                    <FilterBar
                        searchPlaceholder="Search tasks..."
                        filters={filterOptions}
                        onFilterChange={setFilters}
                    />
                </div>
                <div className="flex gap-2">
                    <Button
                        variant={viewMode === 'grid' ? 'solid' : 'default'}
                        icon={<HiOutlineViewGrid />}
                        onClick={() => setViewMode('grid')}
                        size="sm"
                    />
                    <Button
                        variant={viewMode === 'list' ? 'solid' : 'default'}
                        icon={<HiOutlineViewList />}
                        onClick={() => setViewMode('list')}
                        size="sm"
                    />
                </div>
            </div>

            {/* Task Display */}
            {filteredTasks.length > 0 ? (
                <div
                    className={
                        viewMode === 'grid'
                            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                            : 'space-y-3'
                    }
                >
                    {filteredTasks.map((task) => (
                        <TaskCard
                            key={task.id}
                            task={task}
                            compact={viewMode === 'list'}
                            onView={onView}
                            onEdit={onEdit}
                            onStart={onStart}
                            onComplete={onComplete}
                            onComment={onComment}
                        />
                    ))}
                </div>
            ) : (
                <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                        <svg
                            className="w-16 h-16 mx-auto"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={1}
                                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
                            />
                        </svg>
                    </div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                        No tasks found
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                        {Object.keys(filters).length > 0 || activeTab !== 'all'
                            ? 'Try adjusting your filters or tab selection to find what you need.'
                            : 'Get started by creating your first compliance task.'}
                    </p>
                    <Button
                        variant="solid"
                        icon={<HiPlus />}
                        onClick={onAddTask}
                        className="bg-emerald-500 hover:bg-emerald-600"
                    >
                        Add Task
                    </Button>
                </div>
            )}
        </div>
    )
}

export default TaskList
