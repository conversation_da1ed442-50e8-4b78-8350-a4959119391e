'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import StatusBadge from '../components/StatusBadge'
import Switcher from '@/components/ui/Switcher'
import {
    PiInfoDuotone,
    PiShieldCheckDuotone,
    PiLeafDuotone,
    PiRobotDuotone,
    PiBookOpenDuotone,
    PiCurrencyDollarDuotone,
    PiUserGearDuotone,
    PiClockCountdownDuotone,
} from 'react-icons/pi'

const AddOnsTab = () => {
    const [activeAddOns, setActiveAddOns] = useState<string[]>([
        'risk-management',
        'training-lms',
    ])

    const toggleAddOn = (id: string) => {
        if (activeAddOns.includes(id)) {
            setActiveAddOns(activeAddOns.filter((item) => item !== id))
        } else {
            setActiveAddOns([...activeAddOns, id])
        }
    }

    // Get icon for industry
    const getIndustryIcon = (industry: string) => {
        switch (industry) {
            case 'Cybersecurity':
                return <PiShieldCheckDuotone className="text-blue-500" />
            case 'ESG':
                return <PiLeafDuotone className="text-green-500" />
            case 'AI Ethics':
                return <PiRobotDuotone className="text-purple-500" />
            case 'Strategic':
                return <PiBookOpenDuotone className="text-amber-500" />
            case 'Financial':
                return <PiCurrencyDollarDuotone className="text-emerald-500" />
            case 'Training':
                return <PiUserGearDuotone className="text-indigo-500" />
            case 'Audit':
                return <PiClockCountdownDuotone className="text-red-500" />
            default:
                return <PiInfoDuotone className="text-gray-500" />
        }
    }

    // Get color for industry badge
    const getIndustryColor = (industry: string) => {
        switch (industry) {
            case 'Cybersecurity':
                return 'bg-blue-100 text-blue-600 border border-blue-200'
            case 'ESG':
                return 'bg-green-100 text-green-600 border border-green-200'
            case 'AI Ethics':
                return 'bg-purple-100 text-purple-600 border border-purple-200'
            case 'Strategic':
                return 'bg-amber-100 text-amber-600 border border-amber-200'
            case 'Financial':
                return 'bg-emerald-100 text-emerald-600 border border-emerald-200'
            case 'Training':
                return 'bg-indigo-100 text-indigo-600 border border-indigo-200'
            case 'Audit':
                return 'bg-red-100 text-red-600 border border-red-200'
            default:
                return 'bg-gray-100 text-gray-600 border border-gray-200'
        }
    }

    // Calculate total price
    const calculateTotalPrice = () => {
        let total = 0
        activeAddOns.forEach((id) => {
            const addon = addOns.find((a) => a.id === id)
            if (addon && addon.price !== 'Included') {
                total += parseInt(
                    addon.price.replace('£', '').replace('/mo', ''),
                )
            }
        })
        return `£${total}/mo`
    }

    return (
        <div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <div className="lg:col-span-2">
                    <h5 className="font-bold text-lg mb-4">
                        Available Add-Ons
                    </h5>

                    <div className="space-y-4">
                        {addOns.map((addon) => (
                            <Card key={addon.id} className="overflow-hidden">
                                <div className="flex flex-col md:flex-row">
                                    <div className="flex-grow p-4">
                                        <div className="flex items-start mb-3">
                                            <span className="text-2xl mr-3 mt-1">
                                                {getIndustryIcon(
                                                    addon.industry,
                                                )}
                                            </span>
                                            <div>
                                                <h6 className="font-semibold text-lg">
                                                    {addon.name}
                                                </h6>
                                                <div className="flex flex-wrap gap-2 mt-1">
                                                    <StatusBadge
                                                        className={getIndustryColor(
                                                            addon.industry,
                                                        )}
                                                    >
                                                        {addon.industry}
                                                    </StatusBadge>
                                                    <StatusBadge className="bg-purple-100 text-purple-600 border border-purple-200">
                                                        {addon.includedIn}
                                                    </StatusBadge>
                                                </div>
                                            </div>
                                        </div>
                                        <p className="text-sm text-gray-500 mb-3">
                                            {addon.description}
                                        </p>
                                        <div className="flex flex-wrap gap-2">
                                            {addon.features.map(
                                                (feature, index) => (
                                                    <StatusBadge
                                                        key={index}
                                                        className="bg-gray-100 text-gray-600 border border-gray-200"
                                                    >
                                                        {feature}
                                                    </StatusBadge>
                                                ),
                                            )}
                                        </div>
                                    </div>
                                    <div className="bg-gray-50 dark:bg-gray-800 p-4 flex flex-col justify-between md:w-48">
                                        <div className="text-center mb-4">
                                            <div className="text-lg font-bold">
                                                {addon.price}
                                            </div>
                                            {addon.price !== 'Included' && (
                                                <div className="text-xs text-gray-500">
                                                    per month
                                                </div>
                                            )}
                                        </div>
                                        <div className="flex justify-center">
                                            <Switcher
                                                checked={activeAddOns.includes(
                                                    addon.id,
                                                )}
                                                onChange={() =>
                                                    toggleAddOn(addon.id)
                                                }
                                                disabled={
                                                    addon.includedIn ===
                                                        'Enterprise' &&
                                                    !activeAddOns.includes(
                                                        addon.id,
                                                    )
                                                }
                                            />
                                        </div>
                                    </div>
                                </div>
                            </Card>
                        ))}
                    </div>
                </div>

                {/* Summary */}
                <div>
                    <Card>
                        <h5 className="font-bold text-lg mb-4">Your Add-Ons</h5>
                        <div className="space-y-3 mb-4">
                            {activeAddOns.length > 0 ? (
                                <>
                                    {activeAddOns.map((id) => {
                                        const addon = addOns.find(
                                            (a) => a.id === id,
                                        )
                                        return addon ? (
                                            <div
                                                key={id}
                                                className="flex justify-between items-center"
                                            >
                                                <div className="flex items-center">
                                                    <span className="text-lg mr-2">
                                                        {getIndustryIcon(
                                                            addon.industry,
                                                        )}
                                                    </span>
                                                    <span>{addon.name}</span>
                                                </div>
                                                <span className="font-medium">
                                                    {addon.price}
                                                </span>
                                            </div>
                                        ) : null
                                    })}
                                    <div className="border-t border-gray-200 dark:border-gray-700 pt-3 flex justify-between">
                                        <span className="font-medium">
                                            Total Add-Ons
                                        </span>
                                        <span className="font-bold">
                                            {calculateTotalPrice()}
                                        </span>
                                    </div>
                                </>
                            ) : (
                                <div className="text-center py-6 text-gray-500">
                                    No add-ons selected
                                </div>
                            )}
                        </div>

                        <Button
                            block
                            variant="solid"
                            className="bg-emerald-500 hover:bg-emerald-600"
                            disabled={activeAddOns.length === 0}
                        >
                            Save Changes
                        </Button>
                    </Card>
                </div>
            </div>
        </div>
    )
}

// Sample data
const addOns = [
    {
        id: 'risk-management',
        name: 'Risk Management',
        industry: 'Cybersecurity',
        description:
            'Comprehensive risk register with linkage to controls and automated risk scoring.',
        features: ['Risk Register', 'Control Mapping', 'Risk Scoring'],
        includedIn: 'Pro+',
        price: 'Included',
    },
    {
        id: 'vendor-management',
        name: 'Vendor Management',
        industry: 'Financial',
        description:
            'SIG Lite / TPRM workspace for managing vendor security assessments.',
        features: ['Vendor Assessments', 'Risk Scoring', 'Document Management'],
        includedIn: 'Enterprise',
        price: '£49/mo',
    },
    {
        id: 'esg-reporting',
        name: 'ESG Reporting',
        industry: 'ESG',
        description:
            'GRI, SASB, SDG alignment and reporting tools for sustainability compliance.',
        features: ['GRI Standards', 'SASB Metrics', 'SDG Alignment'],
        includedIn: 'Pro+',
        price: '£39/mo',
    },
    {
        id: 'ai-governance',
        name: 'AI Governance Tracker',
        industry: 'AI Ethics',
        description:
            'Comply with EU AI Act, ISO 42001 and other AI governance frameworks.',
        features: ['EU AI Act', 'ISO 42001', 'Risk Assessment'],
        includedIn: 'Enterprise',
        price: '£49/mo',
    },
    {
        id: 'policy-assistant',
        name: 'Policy AI Assistant',
        industry: 'Strategic',
        description:
            'Smart policy generation and maintenance with AI-powered recommendations.',
        features: ['Policy Templates', 'AI Generation', 'Version Control'],
        includedIn: 'Add-on',
        price: '£19/mo',
    },
    {
        id: 'training-lms',
        name: 'Training LMS',
        industry: 'Training',
        description:
            'Assign and track compliance training across your organization.',
        features: ['Course Library', 'Tracking', 'Certifications'],
        includedIn: 'Pro+',
        price: '£25/mo',
    },
    {
        id: 'audit-management',
        name: 'Audit Management',
        industry: 'Audit',
        description:
            'Plan, conduct, and track internal and external audits in one place.',
        features: [
            'Audit Planning',
            'Findings Tracking',
            'Evidence Collection',
        ],
        includedIn: 'Enterprise',
        price: '£35/mo',
    },
]

export default AddOnsTab
