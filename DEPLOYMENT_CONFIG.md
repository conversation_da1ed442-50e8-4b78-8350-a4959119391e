# 🚀 Deployment Configuration

## GitHub Branches + Production-Only Vercel

CheckGap uses a simplified deployment strategy with GitHub branch workflow and production-only Vercel deployments.

### Deployment Strategy

#### Development Branch (`develop`)
- **Purpose**: Development and testing
- **CI/CD**: Full validation (tests, security, build, code quality)
- **Deployment**: None (validation only)
- **Merge Target**: `main` branch for production release

#### Production Branch (`main`)

```
https://api.vercel.com/v1/integrations/deploy/prj_6fOZlNufgUp8MXAPhQsOKIWozw3Y/iTCuUJ468F
```

- **Branch**: `main`
- **Environment**: Production
- **URL**: `https://checkgap.com`
- **Trigger**: Automatic deployment on push to main

## GitHub Secrets Configuration

Add these secrets to your GitHub repository (`Settings > Secrets and variables > Actions`):

### Required Secrets

```env
# Vercel Production Deploy Hook
VERCEL_PRODUCTION_DEPLOY_HOOK=https://api.vercel.com/v1/integrations/deploy/prj_6fOZlNufgUp8MXAPhQsOKIWozw3Y/iTCuUJ468F

# Supabase Configuration (for environment variables in Vercel)
NEXT_PUBLIC_SUPABASE_URL=https://uemjkvhfawncjpesfupm.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVlbWprdmhmYXduY2pwZXNmdXBtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3ODYzNjAsImV4cCI6MjA2MjM2MjM2MH0.zSBXphxu0nv9szeA0U0k_iu-QOcpRhWoOljX-cQtVXE
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Branch Strategy

### Two-Branch Setup (GitHub Only)

```
main (production)     ← Production deployment to Vercel
  ↑
develop (validation)  ← CI/CD validation only (no deployment)
```

### Development Flow

1. **Feature Development**: Work on feature branches
2. **Development**: Merge to `develop` → Triggers full CI/CD validation
3. **Production**: Merge `develop` to `main` → Triggers production deployment
4. **Validation**: All code must pass tests, security, and quality checks on develop before merging to main

## Vercel Project Configuration

### Environment Variables in Vercel

Configure these in your Vercel project dashboard:

#### For Staging Environment

```env
NEXT_PUBLIC_SUPABASE_URL=your_staging_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_staging_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_staging_service_role_key
NEXT_PUBLIC_APP_ENV=staging
NODE_ENV=production
```

#### For Production Environment

```env
NEXT_PUBLIC_SUPABASE_URL=your_production_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_service_role_key
NEXT_PUBLIC_APP_ENV=production
NODE_ENV=production
```

## Manual Deployment

### Trigger Staging Deployment

```bash
curl -X POST "https://api.vercel.com/v1/integrations/deploy/prj_ROzQ9yDyE0FXARTutSjeo1dHIvNQ/MosJwsrqEd"
```

### Trigger Production Deployment

```bash
curl -X POST "https://api.vercel.com/v1/integrations/deploy/prj_ROzQ9yDyE0FXARTutSjeo1dHIvNQ/4Xvl4bF63x"
```

## Deployment Status

### Check Deployment Status

- **Vercel Dashboard**: [https://vercel.com/dashboard](https://vercel.com/dashboard)
- **GitHub Actions**: Check the Actions tab in your repository
- **Staging URL**: [https://checkgap-staging.vercel.app](https://checkgap-staging.vercel.app)
- **Production URL**: [https://checkgap.com](https://checkgap.com)

### Deployment Logs

1. Go to Vercel Dashboard
2. Select your project
3. Click on the deployment
4. View build and runtime logs

## Troubleshooting

### Common Issues

#### Deploy Hook Not Working

- Verify the deploy hook URL is correct
- Check GitHub secrets are properly set
- Ensure the branch name matches the configured branch

#### Build Failures

- Check Vercel build logs
- Verify environment variables are set correctly
- Ensure all dependencies are properly installed

#### Environment Variables

- Staging and production should have separate Supabase projects
- Environment variables must be set in Vercel dashboard
- Secrets in GitHub are only for deploy hooks

### Support

- **Vercel Documentation**: [https://vercel.com/docs/deploy-hooks](https://vercel.com/docs/deploy-hooks)
- **GitHub Actions**: Check workflow logs in repository Actions tab
- **Supabase**: [https://supabase.com/docs](https://supabase.com/docs)

## Security Notes

### Deploy Hook Security

- Deploy hooks are public URLs but only trigger deployments
- They don't expose sensitive information
- Only authorized GitHub Actions should use them
- Rotate hooks if compromised

### Environment Variables

- Never commit sensitive keys to repository
- Use GitHub Secrets for CI/CD configuration
- Use Vercel environment variables for runtime configuration
- Separate staging and production credentials
