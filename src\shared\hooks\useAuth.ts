'use client'

import { useContext } from 'react'
import { useSession } from 'next-auth/react'
import SessionContext from '@/components/auth/AuthProvider/SessionContext'
import type { User } from '@/@types/auth'

export const useAuth = () => {
    const { data: session } = useSession()
    const sessionContext = useContext(SessionContext)

    // Use session from context if available, otherwise from next-auth
    const currentSession = sessionContext || session

    const user = currentSession?.user as User | undefined
    const isAuthenticated = !!user
    const userRole = user?.authority?.[0] || 'user' // Get first authority as role
    const isAdmin = userRole === 'admin'
    const isUser = userRole === 'user'

    const hasPermission = (permission: string): boolean => {
        if (!user?.authority) return false
        return user.authority.includes(permission)
    }

    const hasRole = (role: string): boolean => {
        if (!user?.authority) return false
        return user.authority.includes(role)
    }

    const hasAnyRole = (roles: string[]): boolean => {
        if (!user?.authority) return false
        return roles.some((role) => user.authority?.includes(role))
    }

    return {
        user,
        isAuthenticated,
        userRole,
        isAdmin,
        isUser,
        hasPermission,
        hasRole,
        hasAnyRole,
        session: currentSession,
    }
}
