// PCI DSS 4.0.1 specific types and interfaces

export interface PCIDSSRequirement {
    id: string
    number: string
    title: string
    description: string
    category: PCIDSSCategory
    subcategory?: string
    requirements: string[]
    testingProcedures: string[]
    guidance?: string
    customizedApproach?: string
    status: PCIDSSComplianceStatus
    evidenceRequired: string[]
    implementationNotes?: string
    riskLevel: 'Low' | 'Medium' | 'High' | 'Critical'
    businessImpact: 'Low' | 'Medium' | 'High'
    implementationEffort: 'Low' | 'Medium' | 'High'
    owner?: string
    assignee?: string
    dueDate?: string
    lastReviewDate?: string
    nextReviewDate?: string
    createdAt: string
    updatedAt: string
}

export type PCIDSSCategory = 
    | 'Build and Maintain a Secure Network and Systems'
    | 'Protect Cardholder Data'
    | 'Maintain a Vulnerability Management Program'
    | 'Implement Strong Access Control Measures'
    | 'Regularly Monitor and Test Networks'
    | 'Maintain an Information Security Policy'

export type PCIDSSComplianceStatus = 
    | 'Not Started'
    | 'In Progress'
    | 'Implemented'
    | 'Needs Review'
    | 'Compliant'
    | 'Non-Compliant'
    | 'Not Applicable'

export interface PCIDSSFramework {
    id: string
    name: string
    version: string
    description: string
    requirements: PCIDSSRequirement[]
    totalRequirements: number
    completedRequirements: number
    compliancePercentage: number
    lastAssessmentDate?: string
    nextAssessmentDate?: string
    assessor?: string
    status: 'Active' | 'In Progress' | 'Not Started' | 'Archived'
    createdAt: string
    updatedAt: string
    organizationId: string
}

export interface PCIDSSAssessment {
    id: string
    frameworkId: string
    assessmentType: 'Self-Assessment' | 'External-Assessment' | 'Internal-Audit'
    assessor: string
    startDate: string
    endDate?: string
    status: 'Planning' | 'In Progress' | 'Under Review' | 'Completed' | 'Failed'
    findings: PCIDSSFinding[]
    overallScore?: number
    complianceLevel: 'Level 1' | 'Level 2' | 'Level 3' | 'Level 4'
    createdAt: string
    updatedAt: string
}

export interface PCIDSSFinding {
    id: string
    requirementId: string
    severity: 'Low' | 'Medium' | 'High' | 'Critical'
    finding: string
    recommendation: string
    status: 'Open' | 'In Progress' | 'Resolved' | 'Accepted Risk'
    dueDate?: string
    assignee?: string
    createdAt: string
    updatedAt: string
}

export interface PCIDSSEvidence {
    id: string
    requirementId: string
    title: string
    description: string
    type: 'Document' | 'Screenshot' | 'Log File' | 'Certificate' | 'Report' | 'Other'
    filePath?: string
    url?: string
    status: 'Draft' | 'Under Review' | 'Approved' | 'Rejected' | 'Expired'
    expirationDate?: string
    uploadedBy: string
    reviewedBy?: string
    createdAt: string
    updatedAt: string
}

export interface PCIDSSMetrics {
    totalRequirements: number
    compliantRequirements: number
    nonCompliantRequirements: number
    inProgressRequirements: number
    notStartedRequirements: number
    compliancePercentage: number
    criticalFindings: number
    highFindings: number
    mediumFindings: number
    lowFindings: number
    overdueTasks: number
    upcomingDeadlines: number
}

// PCI DSS 4.0.1 requirement categories mapping
export const PCI_DSS_CATEGORIES: Record<string, PCIDSSCategory> = {
    '1': 'Build and Maintain a Secure Network and Systems',
    '2': 'Build and Maintain a Secure Network and Systems',
    '3': 'Protect Cardholder Data',
    '4': 'Protect Cardholder Data',
    '5': 'Maintain a Vulnerability Management Program',
    '6': 'Maintain a Vulnerability Management Program',
    '7': 'Implement Strong Access Control Measures',
    '8': 'Implement Strong Access Control Measures',
    '9': 'Implement Strong Access Control Measures',
    '10': 'Regularly Monitor and Test Networks',
    '11': 'Regularly Monitor and Test Networks',
    '12': 'Maintain an Information Security Policy',
}

// PCI DSS compliance levels
export const PCI_DSS_LEVELS = {
    'Level 1': {
        description: 'Merchants processing over 6 million card transactions annually',
        requirements: 'Full PCI DSS compliance required',
        assessment: 'Annual on-site assessment by QSA',
    },
    'Level 2': {
        description: 'Merchants processing 1-6 million card transactions annually',
        requirements: 'Full PCI DSS compliance required',
        assessment: 'Annual Self-Assessment Questionnaire',
    },
    'Level 3': {
        description: 'Merchants processing 20,000-1 million e-commerce transactions annually',
        requirements: 'Full PCI DSS compliance required',
        assessment: 'Annual Self-Assessment Questionnaire',
    },
    'Level 4': {
        description: 'Merchants processing fewer than 20,000 e-commerce transactions annually',
        requirements: 'PCI DSS compliance required',
        assessment: 'Annual Self-Assessment Questionnaire',
    },
} as const

export type PCIDSSLevel = keyof typeof PCI_DSS_LEVELS
