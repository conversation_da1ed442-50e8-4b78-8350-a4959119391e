'use client'

import { useState } from 'react'
import { User, UserFilter, PaginationOptions } from '../types'

export const useUsers = (
    filters?: UserFilter,
    pagination?: PaginationOptions,
) => {
    // For now, return mock data. In a real implementation, this would
    // convert UserFilter to the appropriate Supabase query
    console.log('User filters:', filters, 'Pagination:', pagination)
    return useMockUsers()
}

// Mock data for development - remove when Supabase is set up
export const useMockUsers = () => {
    const [users] = useState<User[]>([
        {
            id: 'user-1',
            email: '<EMAIL>',
            userName: 'admin',
            firstName: 'John',
            lastName: 'Doe',
            avatar: '/img/avatars/thumb-1.jpg',
            role: 'admin',
            status: 'active',
            lastLogin: '2024-01-15T10:30:00Z',
            createdAt: '2023-01-15T00:00:00Z',
            updatedAt: '2024-01-15T10:30:00Z',
            organizationId: 'default-org',
            permissions: [
                'read:frameworks',
                'write:frameworks',
                'delete:frameworks',
                'read:controls',
                'write:controls',
                'delete:controls',
                'read:tasks',
                'write:tasks',
                'delete:tasks',
                'read:risks',
                'write:risks',
                'delete:risks',
                'read:vendors',
                'write:vendors',
                'delete:vendors',
                'read:policies',
                'write:policies',
                'delete:policies',
                'read:assets',
                'write:assets',
                'delete:assets',
                'read:evidence',
                'write:evidence',
                'delete:evidence',
                'read:users',
                'write:users',
                'delete:users',
                'read:admin',
                'write:admin',
                'read:dashboard',
            ],
        },
        {
            id: 'user-2',
            email: '<EMAIL>',
            userName: 'jane.smith',
            firstName: 'Jane',
            lastName: 'Smith',
            avatar: '/img/avatars/thumb-2.jpg',
            role: 'user',
            status: 'active',
            lastLogin: '2024-01-14T16:45:00Z',
            createdAt: '2023-06-01T00:00:00Z',
            updatedAt: '2024-01-14T16:45:00Z',
            organizationId: 'default-org',
            permissions: [
                'read:frameworks',
                'read:controls',
                'read:tasks',
                'write:tasks',
                'read:risks',
                'read:vendors',
                'read:policies',
                'read:assets',
                'read:evidence',
                'write:evidence',
                'read:dashboard',
            ],
        },
        {
            id: 'user-3',
            email: '<EMAIL>',
            userName: 'mike.johnson',
            firstName: 'Mike',
            lastName: 'Johnson',
            avatar: '/img/avatars/thumb-3.jpg',
            role: 'user',
            status: 'active',
            lastLogin: '2024-01-13T09:15:00Z',
            createdAt: '2023-09-15T00:00:00Z',
            updatedAt: '2024-01-13T09:15:00Z',
            organizationId: 'default-org',
            permissions: [
                'read:frameworks',
                'read:controls',
                'read:tasks',
                'write:tasks',
                'read:risks',
                'read:vendors',
                'read:policies',
                'read:assets',
                'read:evidence',
                'write:evidence',
                'read:dashboard',
            ],
        },
        {
            id: 'user-4',
            email: '<EMAIL>',
            userName: 'sarah.wilson',
            firstName: 'Sarah',
            lastName: 'Wilson',
            avatar: '/img/avatars/thumb-4.jpg',
            role: 'admin',
            status: 'active',
            lastLogin: '2024-01-15T08:20:00Z',
            createdAt: '2023-03-10T00:00:00Z',
            updatedAt: '2024-01-15T08:20:00Z',
            organizationId: 'default-org',
            permissions: [
                'read:frameworks',
                'write:frameworks',
                'delete:frameworks',
                'read:controls',
                'write:controls',
                'delete:controls',
                'read:tasks',
                'write:tasks',
                'delete:tasks',
                'read:risks',
                'write:risks',
                'delete:risks',
                'read:vendors',
                'write:vendors',
                'delete:vendors',
                'read:policies',
                'write:policies',
                'delete:policies',
                'read:assets',
                'write:assets',
                'delete:assets',
                'read:evidence',
                'write:evidence',
                'delete:evidence',
                'read:users',
                'write:users',
                'delete:users',
                'read:admin',
                'write:admin',
                'read:dashboard',
            ],
        },
        {
            id: 'user-5',
            email: '<EMAIL>',
            userName: 'david.brown',
            firstName: 'David',
            lastName: 'Brown',
            avatar: '/img/avatars/thumb-5.jpg',
            role: 'user',
            status: 'inactive',
            lastLogin: '2023-12-20T14:30:00Z',
            createdAt: '2023-08-05T00:00:00Z',
            updatedAt: '2023-12-20T14:30:00Z',
            organizationId: 'default-org',
            permissions: [
                'read:frameworks',
                'read:controls',
                'read:tasks',
                'write:tasks',
                'read:risks',
                'read:vendors',
                'read:policies',
                'read:assets',
                'read:evidence',
                'write:evidence',
                'read:dashboard',
            ],
        },
    ])

    return {
        data: users,
        loading: false,
        error: null,
        total: users.length,
        refetch: () => {},
    }
}
