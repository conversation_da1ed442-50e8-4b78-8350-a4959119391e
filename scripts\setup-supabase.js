#!/usr/bin/env node

/**
 * Supabase Setup Script
 *
 * This script helps you set up your Supabase database with the compliance platform schema
 * and initial data.
 *
 * Usage:
 * node scripts/setup-supabase.js
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY // Service key for admin operations

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
    console.error('❌ Missing required environment variables:')
    console.error('   NEXT_PUBLIC_SUPABASE_URL')
    console.error('   SUPABASE_SERVICE_KEY')
    console.error('')
    console.error('Please add these to your .env.local file')
    process.exit(1)
}

const supabase = createClient(S<PERSON>ABASE_URL, SUPABASE_SERVICE_KEY)

async function setupDatabase() {
    console.log('🚀 Setting up Supabase database for Compliance Platform...')

    try {
        // 1. Create default organization
        console.log('📝 Creating default organization...')
        const { data: org, error: orgError } = await supabase
            .from('organizations')
            .insert({
                name: 'Default Organization',
                domain: 'example.com',
                settings: {},
            })
            .select()
            .single()

        if (orgError && !orgError.message.includes('duplicate key')) {
            throw orgError
        }

        const organizationId = org?.id || 'default-org-id'
        console.log('✅ Organization created:', organizationId)

        // 2. Insert sample frameworks
        console.log('📋 Creating sample frameworks...')
        const frameworks = [
            {
                id: 'pci-dss-401',
                name: 'PCI DSS 4.0.1',
                version: '4.0.1',
                category: 'Cybersecurity',
                description:
                    'Payment Card Industry Data Security Standard for organizations that handle credit cards.',
                region: 'Global',
                status: 'Active',
                control_count: 264,
                progress: 62,
                last_updated: '2023-11-15',
                notifications: 3,
                created_by: 'admin',
                organization_id: organizationId,
            },
            {
                id: 'iso-27001',
                name: 'ISO/IEC 27001',
                version: '2022',
                category: 'Cybersecurity',
                description:
                    'International standard for information security management systems.',
                region: 'Global',
                status: 'Active',
                control_count: 114,
                progress: 78,
                last_updated: '2023-10-22',
                notifications: 0,
                created_by: 'admin',
                organization_id: organizationId,
            },
        ]

        const { error: frameworkError } = await supabase
            .from('frameworks')
            .upsert(frameworks)

        if (frameworkError) throw frameworkError
        console.log('✅ Sample frameworks created')

        // 3. Insert sample controls
        console.log('🎛️ Creating sample controls...')
        const controls = [
            {
                id: 'pci-3.4.1',
                framework_id: 'pci-dss-401',
                control_number: '3.4.1',
                title: 'Strong cryptography and security protocols are used',
                description:
                    'Strong cryptography and security protocols are used to safeguard sensitive account data during transmission over open, public networks.',
                category: 'Data Protection',
                requirements: [
                    'Use strong cryptography',
                    'Secure transmission protocols',
                ],
                status: 'Implemented',
                evidence_readiness: 'Ready',
                risk_level: 'High',
                implementation_effort: 'High',
                business_impact: 'Medium',
                tags: ['encryption', 'transmission', 'cryptography'],
                organization_id: organizationId,
            },
            {
                id: 'pci-7.1.1',
                framework_id: 'pci-dss-401',
                control_number: '7.1.1',
                title: 'Limit access to system components and cardholder data',
                description:
                    'Limit access to system components and cardholder data to only those individuals whose job requires such access.',
                category: 'Access Control',
                requirements: [
                    'Role-based access control',
                    'Principle of least privilege',
                ],
                status: 'In Progress',
                evidence_readiness: 'Partial',
                risk_level: 'High',
                implementation_effort: 'Medium',
                business_impact: 'Medium',
                tags: ['access-control', 'rbac', 'least-privilege'],
                organization_id: organizationId,
            },
        ]

        const { error: controlError } = await supabase
            .from('controls')
            .upsert(controls)

        if (controlError) throw controlError
        console.log('✅ Sample controls created')

        // 4. Insert sample tasks
        console.log('📋 Creating sample tasks...')
        const tasks = [
            {
                id: 'task-1',
                title: 'Quarterly PCI DSS Network Scan',
                description:
                    'Perform quarterly vulnerability scan of all network segments that handle cardholder data',
                type: 'Recurring',
                frequency: 'Quarterly',
                status: 'Open',
                priority: 'High',
                assignee_id: 'john-doe',
                owner_id: 'security-team',
                due_date: '2024-03-31',
                estimated_hours: 8,
                control_ids: ['pci-3.4.1'],
                framework_ids: ['pci-dss-401'],
                tags: ['vulnerability-scan', 'network', 'quarterly'],
                attachments: [],
                recurrence_rule: {
                    frequency: 'Quarterly',
                    interval: 1,
                },
                organization_id: organizationId,
            },
        ]

        const { error: taskError } = await supabase.from('tasks').upsert(tasks)

        if (taskError) throw taskError
        console.log('✅ Sample tasks created')

        // 5. Insert sample risks
        console.log('⚠️ Creating sample risks...')
        const risks = [
            {
                id: 'risk-1',
                title: 'Cardholder Data Exposure via Unencrypted Storage',
                description:
                    'Risk of cardholder data being exposed due to unencrypted storage in legacy systems.',
                category: 'Compliance',
                likelihood: 3,
                impact: 5,
                status: 'Assessed',
                owner_id: 'security-team',
                control_ids: ['pci-3.4.1'],
                mitigation_plan:
                    'Implement encryption for all cardholder data storage systems',
                mitigation_deadline: '2024-03-31',
                next_review_date: '2024-04-01',
                tags: ['cardholder-data', 'encryption', 'legacy-systems'],
                organization_id: organizationId,
            },
        ]

        const { error: riskError } = await supabase.from('risks').upsert(risks)

        if (riskError) throw riskError
        console.log('✅ Sample risks created')

        console.log('')
        console.log('🎉 Database setup complete!')
        console.log('')
        console.log('Next steps:')
        console.log('1. Start your development server: npm run dev')
        console.log('2. Visit /dashboard/compliance-dashboard to see your data')
        console.log('3. Explore the different modules:')
        console.log('   - /dashboard/framework-management')
        console.log('   - /dashboard/task-engine')
        console.log('   - /dashboard/risk-management')
        console.log('')
    } catch (error) {
        console.error('❌ Error setting up database:', error.message)
        process.exit(1)
    }
}

async function checkConnection() {
    console.log('🔍 Checking Supabase connection...')

    try {
        const { data, error } = await supabase
            .from('organizations')
            .select('count')
            .limit(1)

        if (error) throw error
        console.log('✅ Supabase connection successful')
        return true
    } catch (error) {
        console.error('❌ Supabase connection failed:', error.message)
        console.error('')
        console.error('Please check:')
        console.error('1. Your SUPABASE_URL is correct')
        console.error('2. Your SUPABASE_SERVICE_KEY is correct')
        console.error('3. The database schema has been applied')
        console.error(
            '4. Run: psql -h your-host -U postgres -d postgres -f database-schema.sql',
        )
        return false
    }
}

async function main() {
    console.log('🏗️ Compliance Platform - Supabase Setup')
    console.log('=====================================')
    console.log('')

    const connected = await checkConnection()
    if (!connected) {
        process.exit(1)
    }

    await setupDatabase()
}

// Run the setup
main().catch(console.error)
