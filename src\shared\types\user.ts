// User management types

export type UserRole = 'admin' | 'user'

export type UserStatus = 'active' | 'inactive' | 'pending' | 'suspended'

export interface User {
    id: string
    email: string
    userName: string
    firstName?: string
    lastName?: string
    avatar?: string
    role: UserRole
    status: UserStatus
    lastLogin?: string
    createdAt: string
    updatedAt: string
    organizationId: string
    permissions?: string[]
    metadata?: Record<string, unknown>
}

export interface CreateUserRequest {
    email: string
    userName: string
    firstName?: string
    lastName?: string
    role: UserRole
    sendInvite?: boolean
}

export interface UpdateUserRequest {
    userName?: string
    firstName?: string
    lastName?: string
    role?: UserRole
    status?: UserStatus
    avatar?: string
    permissions?: string[]
}

export interface UserInvitation {
    id: string
    email: string
    role: UserRole
    invitedBy: string
    invitedAt: string
    expiresAt: string
    status: 'pending' | 'accepted' | 'expired' | 'cancelled'
    organizationId: string
}

export interface UserActivity {
    id: string
    userId: string
    action: string
    resource?: string
    details?: Record<string, unknown>
    ipAddress?: string
    userAgent?: string
    timestamp: string
}

export interface UserPermissions {
    // Framework Management
    'read:frameworks': boolean
    'write:frameworks': boolean
    'delete:frameworks': boolean

    // Control Management
    'read:controls': boolean
    'write:controls': boolean
    'delete:controls': boolean

    // Task Management
    'read:tasks': boolean
    'write:tasks': boolean
    'delete:tasks': boolean

    // Risk Management
    'read:risks': boolean
    'write:risks': boolean
    'delete:risks': boolean

    // Vendor Management
    'read:vendors': boolean
    'write:vendors': boolean
    'delete:vendors': boolean

    // Policy Management
    'read:policies': boolean
    'write:policies': boolean
    'delete:policies': boolean

    // Asset Management
    'read:assets': boolean
    'write:assets': boolean
    'delete:assets': boolean

    // Evidence Management
    'read:evidence': boolean
    'write:evidence': boolean
    'delete:evidence': boolean

    // User Management (Admin only)
    'read:users': boolean
    'write:users': boolean
    'delete:users': boolean

    // Admin Settings
    'read:admin': boolean
    'write:admin': boolean

    // Dashboard Access
    'read:dashboard': boolean
}

export const DEFAULT_USER_PERMISSIONS: UserPermissions = {
    'read:frameworks': true,
    'write:frameworks': false,
    'delete:frameworks': false,
    'read:controls': true,
    'write:controls': false,
    'delete:controls': false,
    'read:tasks': true,
    'write:tasks': true,
    'delete:tasks': false,
    'read:risks': true,
    'write:risks': false,
    'delete:risks': false,
    'read:vendors': true,
    'write:vendors': false,
    'delete:vendors': false,
    'read:policies': true,
    'write:policies': false,
    'delete:policies': false,
    'read:assets': true,
    'write:assets': false,
    'delete:assets': false,
    'read:evidence': true,
    'write:evidence': true,
    'delete:evidence': false,
    'read:users': false,
    'write:users': false,
    'delete:users': false,
    'read:admin': false,
    'write:admin': false,
    'read:dashboard': true,
}

export const DEFAULT_ADMIN_PERMISSIONS: UserPermissions = {
    'read:frameworks': true,
    'write:frameworks': true,
    'delete:frameworks': true,
    'read:controls': true,
    'write:controls': true,
    'delete:controls': true,
    'read:tasks': true,
    'write:tasks': true,
    'delete:tasks': true,
    'read:risks': true,
    'write:risks': true,
    'delete:risks': true,
    'read:vendors': true,
    'write:vendors': true,
    'delete:vendors': true,
    'read:policies': true,
    'write:policies': true,
    'delete:policies': true,
    'read:assets': true,
    'write:assets': true,
    'delete:assets': true,
    'read:evidence': true,
    'write:evidence': true,
    'delete:evidence': true,
    'read:users': true,
    'write:users': true,
    'delete:users': true,
    'read:admin': true,
    'write:admin': true,
    'read:dashboard': true,
}

export interface UserFilter {
    role?: UserRole
    status?: UserStatus
    search?: string
    organizationId?: string
}

export interface PaginationOptions {
    page: number
    limit: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
}
