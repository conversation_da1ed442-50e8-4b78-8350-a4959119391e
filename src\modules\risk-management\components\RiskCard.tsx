'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { StatusBadge } from '@/shared/components'
import { Risk } from '@/shared/types/compliance'
import { getRiskLevel } from '@/shared/utils/compliance'
import {
    HiOutlineExclamationCircle,
    HiOutlineShieldCheck,
    HiOutlineEye,
    HiOutlineCog,
    HiOutlineCalendar,
    HiOutlineRefresh,
} from 'react-icons/hi'

interface RiskCardProps {
    risk: Risk
    onView?: (risk: Risk) => void
    onEdit?: (risk: Risk) => void
    onViewControls?: (risk: Risk) => void
    onUpdateStatus?: (risk: Risk) => void
    compact?: boolean
}

const RiskCard = ({
    risk,
    onView,
    onEdit,
    onViewControls,
    onUpdateStatus,
    compact = false,
}: RiskCardProps) => {
    const [isHovered, setIsHovered] = useState(false)

    const riskLevel = getRiskLevel(risk.riskScore)

    const getCategoryColor = (category: string) => {
        const colors = {
            Operational: 'bg-blue-100 text-blue-700 border-blue-200',
            Financial: 'bg-green-100 text-green-700 border-green-200',
            Strategic: 'bg-purple-100 text-purple-700 border-purple-200',
            Compliance: 'bg-amber-100 text-amber-700 border-amber-200',
            Reputational: 'bg-pink-100 text-pink-700 border-pink-200',
            Technology: 'bg-indigo-100 text-indigo-700 border-indigo-200',
        }
        return colors[category as keyof typeof colors] || colors['Operational']
    }

    const getRiskScoreColor = (score: number) => {
        if (score >= 20) return 'text-purple-600 bg-purple-100'
        if (score >= 12) return 'text-red-600 bg-red-100'
        if (score >= 6) return 'text-amber-600 bg-amber-100'
        return 'text-green-600 bg-green-100'
    }

    if (compact) {
        return (
            <div
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-colors border-gray-200 dark:border-gray-700"
                onClick={() => onView?.(risk)}
            >
                <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                        <HiOutlineExclamationCircle
                            className={`w-4 h-4 ${
                                riskLevel === 'Critical'
                                    ? 'text-purple-500'
                                    : riskLevel === 'High'
                                      ? 'text-red-500'
                                      : riskLevel === 'Medium'
                                        ? 'text-amber-500'
                                        : 'text-green-500'
                            }`}
                        />
                        <div
                            className={`px-2 py-1 rounded text-xs font-medium ${getRiskScoreColor(risk.riskScore)}`}
                        >
                            {risk.riskScore}
                        </div>
                    </div>

                    <div className="flex-1 min-w-0">
                        <h4 className="font-medium truncate">{risk.title}</h4>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                            <span>{risk.category}</span>
                            <span>•</span>
                            <span>
                                L{risk.likelihood} × I{risk.impact}
                            </span>
                        </div>
                    </div>
                </div>

                <div className="flex items-center gap-2">
                    <StatusBadge status={risk.status} size="sm" />
                    <StatusBadge status={riskLevel} size="sm" />
                </div>
            </div>
        )
    }

    return (
        <Card
            clickable
            className="h-full transition-all duration-200 hover:shadow-lg"
            bodyClass="h-full flex flex-col p-4"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={() => onView?.(risk)}
        >
            {/* Header */}
            <div className="flex justify-between items-start mb-3">
                <div className="flex items-center gap-2">
                    <HiOutlineExclamationCircle
                        className={`w-5 h-5 ${
                            riskLevel === 'Critical'
                                ? 'text-purple-500'
                                : riskLevel === 'High'
                                  ? 'text-red-500'
                                  : riskLevel === 'Medium'
                                    ? 'text-amber-500'
                                    : 'text-green-500'
                        }`}
                    />
                    <StatusBadge status={risk.status} size="sm" />
                </div>
                <div
                    className={`px-3 py-1 rounded-lg text-sm font-bold ${getRiskScoreColor(risk.riskScore)}`}
                >
                    {risk.riskScore}
                </div>
            </div>

            {/* Risk Info */}
            <div className="flex-1">
                <h4 className="font-semibold mb-2 line-clamp-2">
                    {risk.title}
                </h4>

                <div
                    className={`inline-block px-2 py-1 rounded-md text-xs font-medium border mb-3 ${getCategoryColor(risk.category)}`}
                >
                    {risk.category}
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-3">
                    {risk.description}
                </p>

                {/* Risk Matrix */}
                <div className="grid grid-cols-2 gap-3 mb-3 text-sm">
                    <div className="bg-gray-50 dark:bg-gray-800 p-2 rounded">
                        <div className="text-xs text-gray-500 mb-1">
                            Likelihood
                        </div>
                        <div className="font-medium">{risk.likelihood}/5</div>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-800 p-2 rounded">
                        <div className="text-xs text-gray-500 mb-1">Impact</div>
                        <div className="font-medium">{risk.impact}/5</div>
                    </div>
                </div>

                {/* Risk Levels */}
                {(risk.inherentRisk || risk.residualRisk) && (
                    <div className="grid grid-cols-2 gap-3 mb-3 text-xs">
                        {risk.inherentRisk && (
                            <div>
                                <span className="text-gray-500">
                                    Inherent:{' '}
                                </span>
                                <span className="font-medium">
                                    {risk.inherentRisk}
                                </span>
                            </div>
                        )}
                        {risk.residualRisk && (
                            <div>
                                <span className="text-gray-500">
                                    Residual:{' '}
                                </span>
                                <span className="font-medium">
                                    {risk.residualRisk}
                                </span>
                            </div>
                        )}
                    </div>
                )}
            </div>

            {/* Meta Info */}
            <div className="flex justify-between items-center mb-3 text-xs text-gray-500">
                <div className="flex items-center gap-3">
                    <div className="flex items-center gap-1">
                        <HiOutlineShieldCheck className="w-3 h-3" />
                        <span>{risk.controlIds.length} controls</span>
                    </div>
                    {risk.nextReviewDate && (
                        <div className="flex items-center gap-1">
                            <HiOutlineCalendar className="w-3 h-3" />
                            <span>
                                Review{' '}
                                {new Date(
                                    risk.nextReviewDate,
                                ).toLocaleDateString()}
                            </span>
                        </div>
                    )}
                </div>
                <StatusBadge status={riskLevel} size="sm" />
            </div>

            {/* Footer */}
            <div className="flex justify-between items-center pt-3 border-t border-gray-200 dark:border-gray-700">
                <div className="text-xs text-gray-500">
                    {risk.owner && `Owner: ${risk.owner}`}
                </div>

                {/* Action Buttons */}
                <div
                    className={`flex gap-1 transition-opacity duration-200 ${isHovered ? 'opacity-100' : 'opacity-0'}`}
                >
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineEye />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onView?.(risk)
                        }}
                        title="View Details"
                    />
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineShieldCheck />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onViewControls?.(risk)
                        }}
                        title="View Controls"
                    />
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineRefresh />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onUpdateStatus?.(risk)
                        }}
                        title="Update Status"
                    />
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineCog />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onEdit?.(risk)
                        }}
                        title="Settings"
                    />
                </div>
            </div>
        </Card>
    )
}

export default RiskCard
