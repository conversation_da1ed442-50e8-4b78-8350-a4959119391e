# Supabase Authentication Setup Guide

This guide will help you set up Google Sign-in with <PERSON><PERSON><PERSON> in your Next.js application.

## Prerequisites

1. A Supabase account and project
2. A Google Cloud Console project with OAuth 2.0 credentials

## Step 1: Supabase Project Setup

### 1.1 Create a Supabase Project

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Click "New Project"
3. Fill in your project details
4. Wait for the project to be created

### 1.2 Get Your Supabase Credentials

1. Go to Settings > API
2. Copy your Project URL and anon public key
3. Add them to your `.env.local` file:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Step 2: Google OAuth Setup

### 2.1 Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API
4. Go to Credentials > Create Credentials > OAuth 2.0 Client IDs
5. Configure the OAuth consent screen
6. Create OAuth 2.0 credentials:
    - Application type: Web application
    - Authorized redirect URIs: `https://your-project-ref.supabase.co/auth/v1/callback`

### 2.2 Configure Supabase Authentication

1. In your Supabase dashboard, go to Authentication > Providers
2. Enable Google provider
3. Add your Google OAuth credentials:
    - Client ID: From Google Cloud Console
    - Client Secret: From Google Cloud Console
4. Add your site URL in Authentication > URL Configuration:
    - Site URL: `http://localhost:3000` (for development)
    - Redirect URLs: `http://localhost:3000/hub` (or your authenticated entry path)

## Step 3: Environment Variables

Create a `.env.local` file in your project root:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Optional: Keep NextAuth for backward compatibility
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_secret_key
```

## Step 4: Test the Integration

1. Start your development server:

    ```bash
    npm run dev
    ```

2. Navigate to `/sign-in`
3. Click the "Google" button
4. You should be redirected to Google's OAuth flow
5. After successful authentication, you'll be redirected to `/hub`

## Features Implemented

### ✅ Authentication Methods

- [x] Email/Password sign-in
- [x] Google OAuth sign-in
- [x] GitHub OAuth sign-in (if configured)
- [x] Password reset via email
- [x] User registration

### ✅ Security Features

- [x] Protected routes with middleware
- [x] Automatic session management
- [x] Secure cookie handling
- [x] CSRF protection

### ✅ User Experience

- [x] Automatic redirects after auth
- [x] Loading states
- [x] Error handling
- [x] User profile dropdown
- [x] Sign out functionality

## File Structure

```
src/
├── contexts/
│   └── AuthContext.tsx          # Main auth context
├── lib/
│   └── supabase/
│       ├── client.ts            # Browser client
│       └── server.ts            # Server client
├── services/
│   └── SupabaseAuthService.ts   # Auth service methods
├── utils/hooks/
│   └── useRequireAuth.ts        # Protected route hook
└── middleware.ts                # Route protection
```

## Troubleshooting

### Common Issues

1. **OAuth redirect mismatch**

    - Ensure your redirect URI in Google Console matches Supabase exactly
    - Format: `https://your-project-ref.supabase.co/auth/v1/callback`

2. **Environment variables not loading**

    - Restart your development server after adding new env vars
    - Ensure variables start with `NEXT_PUBLIC_` for client-side access

3. **User not redirected after sign-in**

    - Check your site URL and redirect URLs in Supabase dashboard
    - Verify your `authenticatedEntryPath` in `app.config.ts`

4. **Session not persisting**
    - Check browser cookies are enabled
    - Verify middleware is properly configured

### Debug Mode

To enable debug logging, add this to your environment:

```env
NEXT_PUBLIC_SUPABASE_DEBUG=true
```

## Production Deployment

1. Update your Supabase project settings:

    - Site URL: Your production domain
    - Redirect URLs: Your production auth callback URLs

2. Update Google OAuth settings:

    - Add production domain to authorized origins
    - Add production callback URL

3. Set production environment variables in your hosting platform

## Next Steps

- Set up Row Level Security (RLS) policies in Supabase
- Configure email templates for password reset
- Add user profile management
- Implement role-based access control
- Set up database triggers for user creation
