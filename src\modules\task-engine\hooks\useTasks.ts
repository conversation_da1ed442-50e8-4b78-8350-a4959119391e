'use client'

import { useState, useCallback } from 'react'
import { useTasks as useTasksBase, useSupabaseMutation } from '@/shared/hooks'
import { Task, TaskDetail, TaskFilter, PaginationOptions } from '../types'
import { exportToCSV } from '@/shared/utils/compliance'

export const useTasks = (
    filters?: TaskFilter,
    pagination?: PaginationOptions,
) => {
    return useTasksBase(filters, pagination)
}

export const useTaskMutations = () => {
    const { create, update, remove, loading, error } =
        useSupabaseMutation<Task>('tasks')

    const createTask = useCallback(
        async (taskData: Partial<Task>) => {
            const newTask = {
                ...taskData,
                id: crypto.randomUUID(),
                status: 'Open' as const,
                comments: [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                organizationId: 'default-org', // This should come from auth context
            }

            return await create(newTask)
        },
        [create],
    )

    const updateTask = useCallback(
        async (id: string, updates: Partial<Task>) => {
            const updatedData = {
                ...updates,
                updatedAt: new Date().toISOString(),
            }

            return await update(id, updatedData)
        },
        [update],
    )

    const deleteTask = useCallback(
        async (id: string) => {
            return await remove(id)
        },
        [remove],
    )

    const startTask = useCallback(
        async (id: string) => {
            return await updateTask(id, { status: 'In Progress' })
        },
        [updateTask],
    )

    const completeTask = useCallback(
        async (id: string) => {
            return await updateTask(id, {
                status: 'Completed',
                completedDate: new Date().toISOString(),
            })
        },
        [updateTask],
    )

    return {
        createTask,
        updateTask,
        deleteTask,
        startTask,
        completeTask,
        loading,
        error,
    }
}

export const useTaskExport = () => {
    const exportTasksToCSV = useCallback((tasks: TaskDetail[]) => {
        const exportData = tasks.map((task) => ({
            'Task ID': task.id,
            Title: task.title,
            Description: task.description || '',
            Status: task.status,
            Priority: task.priority,
            Type: task.type,
            'Due Date': task.dueDate,
            Assignee: task.assigneeDetails?.name || '',
            Owner: task.ownerDetails?.name || '',
            Framework: task.framework?.name || '',
            Controls: task.controls.map((c) => c.title).join('; '),
            Tags: task.tags.join(', '),
            'Estimated Hours': task.estimatedHours || '',
            'Actual Hours': task.actualHours || '',
            'Created Date': task.createdAt,
            'Updated Date': task.updatedAt,
        }))

        exportToCSV(
            exportData,
            `tasks-export-${new Date().toISOString().split('T')[0]}`,
        )
    }, [])

    return { exportTasksToCSV }
}

// Mock data for development - remove when Supabase is set up
export const useMockTasks = () => {
    const [tasks] = useState<TaskDetail[]>([
        {
            id: 'task-1',
            title: 'Quarterly PCI DSS Network Scan',
            description:
                'Perform quarterly vulnerability scan of all network segments that handle cardholder data',
            type: 'Recurring',
            frequency: 'Quarterly',
            status: 'Open',
            priority: 'High',
            assignee: 'john-doe',
            owner: 'security-team',
            dueDate: '2024-01-15',
            estimatedHours: 8,
            controlIds: ['pci-11.2.1', 'pci-11.2.2'],
            frameworkIds: ['pci-dss-401'],
            tags: ['vulnerability-scan', 'network', 'quarterly'],
            attachments: [],
            comments: [
                {
                    id: 'comment-1',
                    taskId: 'task-1',
                    content: 'Scheduled scan for next week',
                    author: 'john-doe',
                    createdAt: '2024-01-08T10:00:00Z',
                },
            ],
            recurrenceRule: {
                frequency: 'Quarterly',
                interval: 1,
            },
            createdAt: '2023-10-15T00:00:00Z',
            updatedAt: '2024-01-08T10:00:00Z',
            organizationId: 'default-org',
            assigneeDetails: {
                id: 'john-doe',
                name: 'John Doe',
                email: '<EMAIL>',
            },
            controls: [
                {
                    id: 'pci-11.2.1',
                    frameworkId: 'pci-dss-401',
                    controlNumber: '11.2.1',
                    title: 'Run internal vulnerability scans',
                    description:
                        'Run internal vulnerability scans at least quarterly',
                    category: 'Testing',
                    requirements: [
                        'Quarterly internal scans',
                        'Remediate high-risk vulnerabilities',
                    ],
                    status: 'In Progress',
                    evidenceReadiness: 'Partial',
                    riskLevel: 'High',
                    implementationEffort: 'Medium',
                    businessImpact: 'Medium',
                    tags: ['vulnerability', 'internal', 'quarterly'],
                    createdAt: '2023-01-15T00:00:00Z',
                    updatedAt: '2024-01-08T00:00:00Z',
                    organizationId: 'default-org',
                },
            ],
        },
        {
            id: 'task-2',
            title: 'Monthly Access Review',
            description:
                'Review user access permissions and remove unnecessary privileges',
            type: 'Recurring',
            frequency: 'Monthly',
            status: 'Overdue',
            priority: 'Critical',
            assignee: 'jane-smith',
            owner: 'compliance-team',
            dueDate: '2024-01-01',
            estimatedHours: 4,
            actualHours: 6,
            controlIds: ['pci-7.1.1', 'pci-8.1.1'],
            frameworkIds: ['pci-dss-401'],
            tags: ['access-review', 'monthly', 'permissions'],
            attachments: [],
            comments: [],
            recurrenceRule: {
                frequency: 'Monthly',
                interval: 1,
            },
            createdAt: '2023-06-01T00:00:00Z',
            updatedAt: '2024-01-02T00:00:00Z',
            organizationId: 'default-org',
            assigneeDetails: {
                id: 'jane-smith',
                name: 'Jane Smith',
                email: '<EMAIL>',
            },
            controls: [
                {
                    id: 'pci-7.1.1',
                    frameworkId: 'pci-dss-401',
                    controlNumber: '7.1.1',
                    title: 'Limit access to system components',
                    description:
                        'Limit access to system components and cardholder data to only those individuals whose job requires such access',
                    category: 'Access Control',
                    requirements: [
                        'Role-based access control',
                        'Principle of least privilege',
                    ],
                    status: 'Implemented',
                    evidenceReadiness: 'Ready',
                    riskLevel: 'High',
                    implementationEffort: 'High',
                    businessImpact: 'Medium',
                    tags: ['access-control', 'rbac'],
                    createdAt: '2023-01-15T00:00:00Z',
                    updatedAt: '2024-01-01T00:00:00Z',
                    organizationId: 'default-org',
                },
            ],
        },
        {
            id: 'task-3',
            title: 'Update Incident Response Plan',
            description:
                'Review and update the incident response plan based on recent security incidents',
            type: 'One-time',
            status: 'In Progress',
            priority: 'Medium',
            assignee: 'mike-johnson',
            owner: 'security-team',
            dueDate: '2024-01-20',
            estimatedHours: 12,
            actualHours: 8,
            controlIds: ['pci-12.10.1'],
            frameworkIds: ['pci-dss-401'],
            tags: ['incident-response', 'documentation', 'security'],
            attachments: [],
            comments: [
                {
                    id: 'comment-2',
                    taskId: 'task-3',
                    content: 'Started reviewing current plan',
                    author: 'mike-johnson',
                    createdAt: '2024-01-10T14:00:00Z',
                },
            ],
            createdAt: '2024-01-05T00:00:00Z',
            updatedAt: '2024-01-10T14:00:00Z',
            organizationId: 'default-org',
            assigneeDetails: {
                id: 'mike-johnson',
                name: 'Mike Johnson',
                email: '<EMAIL>',
            },
            controls: [
                {
                    id: 'pci-12.10.1',
                    frameworkId: 'pci-dss-401',
                    controlNumber: '12.10.1',
                    title: 'Create incident response plan',
                    description:
                        'Create an incident response plan to be followed in the event of system breach',
                    category: 'Policy',
                    requirements: [
                        'Incident response procedures',
                        'Communication plan',
                        'Recovery procedures',
                    ],
                    status: 'Needs Review',
                    evidenceReadiness: 'Partial',
                    riskLevel: 'High',
                    implementationEffort: 'High',
                    businessImpact: 'High',
                    tags: ['incident-response', 'policy'],
                    createdAt: '2023-01-15T00:00:00Z',
                    updatedAt: '2024-01-10T00:00:00Z',
                    organizationId: 'default-org',
                },
            ],
        },
    ])

    return {
        data: tasks,
        loading: false,
        error: null,
        total: tasks.length,
        refetch: () => {},
    }
}
