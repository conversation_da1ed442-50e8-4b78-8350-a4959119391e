'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Progress from '@/components/ui/Progress'
import Button from '@/components/ui/Button'
import { StatusBadge } from '@/shared/components'
import { Framework } from '../types'
import {
    HiOutlineCog,
    HiOutlineEye,
    HiOutlineDocumentText,
    HiOutlineClipboardCheck,
} from 'react-icons/hi'

interface FrameworkCardProps {
    framework: Framework
    onView?: (framework: Framework) => void
    onEdit?: (framework: Framework) => void
    onManageControls?: (framework: Framework) => void
    onViewReports?: (framework: Framework) => void
}

const FrameworkCard = ({
    framework,
    onView,
    onEdit,
    onManageControls,
    onViewReports,
}: FrameworkCardProps) => {
    const [isHovered, setIsHovered] = useState(false)

    const getCategoryColor = (category: string) => {
        const colors = {
            Cybersecurity:
                'bg-blue-100 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-400',
            Privacy:
                'bg-purple-100 text-purple-700 border-purple-200 dark:bg-purple-900/30 dark:text-purple-400',
            Financial:
                'bg-green-100 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-400',
            Healthcare:
                'bg-red-100 text-red-700 border-red-200 dark:bg-red-900/30 dark:text-red-400',
            Environmental:
                'bg-emerald-100 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400',
            Quality:
                'bg-indigo-100 text-indigo-700 border-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-400',
            Strategic:
                'bg-orange-100 text-orange-700 border-orange-200 dark:bg-orange-900/30 dark:text-orange-400',
            'AI Ethics':
                'bg-pink-100 text-pink-700 border-pink-200 dark:bg-pink-900/30 dark:text-pink-400',
        }
        return (
            colors[category as keyof typeof colors] || colors['Cybersecurity']
        )
    }

    return (
        <Card
            clickable
            className="h-full transition-all duration-200 hover:shadow-lg"
            bodyClass="h-full flex flex-col p-6"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={() => onView?.(framework)}
        >
            {/* Header */}
            <div className="flex justify-between items-start mb-4">
                <div className="flex items-center gap-2">
                    <StatusBadge status={framework.status} size="sm" />
                    {framework.notifications > 0 && (
                        <div className="w-5 h-5 rounded-full bg-red-500 flex items-center justify-center">
                            <span className="text-white text-xs font-medium">
                                {framework.notifications}
                            </span>
                        </div>
                    )}
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                    {framework.region}
                </span>
            </div>

            {/* Framework Info */}
            <div className="flex-1">
                <h4 className="font-semibold text-lg mb-2 line-clamp-2">
                    {framework.name}
                </h4>

                <div
                    className={`inline-block px-2 py-1 rounded-md text-xs font-medium border mb-3 ${getCategoryColor(framework.category)}`}
                >
                    {framework.category}
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
                    {framework.description}
                </p>
            </div>

            {/* Progress Section */}
            <div className="mb-4">
                <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600 dark:text-gray-400">
                        {framework.controlCount} Controls
                    </span>
                    <span className="font-medium">
                        {framework.progress}% Complete
                    </span>
                </div>
                <Progress percent={framework.progress} size="sm" />
            </div>

            {/* Footer */}
            <div className="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="text-xs text-gray-500 dark:text-gray-400">
                    Updated:{' '}
                    {new Date(framework.lastUpdated).toLocaleDateString()}
                </div>

                {/* Action Buttons */}
                <div
                    className={`flex gap-1 transition-opacity duration-200 ${isHovered ? 'opacity-100' : 'opacity-0'}`}
                >
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineEye />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onView?.(framework)
                        }}
                        title="View Details"
                    />
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineClipboardCheck />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onManageControls?.(framework)
                        }}
                        title="Manage Controls"
                    />
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineDocumentText />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onViewReports?.(framework)
                        }}
                        title="View Reports"
                    />
                    <Button
                        size="xs"
                        variant="plain"
                        icon={<HiOutlineCog />}
                        onClick={(e) => {
                            e.stopPropagation()
                            onEdit?.(framework)
                        }}
                        title="Settings"
                    />
                </div>
            </div>
        </Card>
    )
}

export default FrameworkCard
