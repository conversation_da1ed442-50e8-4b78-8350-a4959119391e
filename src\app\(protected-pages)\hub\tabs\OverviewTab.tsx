'use client'

import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import StatusBadge from '../components/StatusBadge'
import Progress from '@/components/ui/Progress'
import {
    PiArrowUpRightDuotone,
    PiStackDuotone,
    PiPuzzlePieceDuotone,
    PiChartLineUpDuotone,
} from 'react-icons/pi'

const OverviewTab = () => {
    return (
        <div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                {/* Current Plan Summary */}
                <Card className="lg:col-span-2">
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                        <div>
                            <h5 className="font-bold text-lg mb-1">
                                Current Plan
                            </h5>
                            <div className="flex items-center">
                                <StatusBadge className="bg-emerald-100 text-emerald-600 border border-emerald-200 mr-2">
                                    Growth
                                </StatusBadge>
                                <span className="text-sm text-gray-500">
                                    Renews on Jan 15, 2024
                                </span>
                            </div>
                        </div>
                        <Button
                            size="sm"
                            variant="solid"
                            className="mt-2 md:mt-0 bg-emerald-500 hover:bg-emerald-600"
                            icon={<PiArrowUpRightDuotone />}
                        >
                            Upgrade Plan
                        </Button>
                    </div>

                    <div className="mb-4">
                        <div className="flex justify-between text-sm mb-1">
                            <span>Plan Usage</span>
                            <span>3 of 5 frameworks activated</span>
                        </div>
                        <Progress
                            percent={60}
                            customColorClass="bg-emerald-500"
                        />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                            <div className="flex items-center mb-2">
                                <span className="text-emerald-500 mr-2">
                                    <PiStackDuotone className="text-xl" />
                                </span>
                                <span className="font-medium">Frameworks</span>
                            </div>
                            <div className="text-2xl font-bold">3/5</div>
                            <div className="text-xs text-gray-500">
                                L3 frameworks activated
                            </div>
                        </div>
                        <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                            <div className="flex items-center mb-2">
                                <span className="text-blue-500 mr-2">
                                    <PiPuzzlePieceDuotone className="text-xl" />
                                </span>
                                <span className="font-medium">Add-ons</span>
                            </div>
                            <div className="text-2xl font-bold">2</div>
                            <div className="text-xs text-gray-500">
                                Active add-ons
                            </div>
                        </div>
                        <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                            <div className="flex items-center mb-2">
                                <span className="text-purple-500 mr-2">
                                    <PiChartLineUpDuotone className="text-xl" />
                                </span>
                                <span className="font-medium">
                                    Compliance Score
                                </span>
                            </div>
                            <div className="text-2xl font-bold">78%</div>
                            <div className="text-xs text-gray-500">
                                Across all frameworks
                            </div>
                        </div>
                    </div>
                </Card>

                {/* Quick Actions */}
                <Card>
                    <h5 className="font-bold text-lg mb-4">Quick Actions</h5>
                    <div className="space-y-3">
                        <Button
                            block
                            variant="default"
                            className="justify-start border border-gray-200 dark:border-gray-600"
                            icon={<PiStackDuotone className="text-xl" />}
                        >
                            Add New Framework
                        </Button>
                        <Button
                            block
                            variant="default"
                            className="justify-start border border-gray-200 dark:border-gray-600"
                            icon={<PiStackDuotone className="text-xl" />}
                        >
                            Explore Frameworks
                        </Button>
                        <Button
                            block
                            variant="default"
                            className="justify-start border border-gray-200 dark:border-gray-600"
                            icon={<PiChartLineUpDuotone className="text-xl" />}
                        >
                            View Compliance Report
                        </Button>
                    </div>
                </Card>
            </div>

            {/* Active Frameworks */}
            <Card className="mt-4">
                <h5 className="font-bold text-lg mb-4">Active Frameworks</h5>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {activeFrameworks.map((framework) => (
                        <div
                            key={framework.id}
                            className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
                        >
                            <div className="flex justify-between items-start mb-2">
                                <StatusBadge
                                    className={`${framework.categoryColor} text-xs`}
                                >
                                    {framework.category}
                                </StatusBadge>
                                <StatusBadge className="bg-emerald-100 text-emerald-600 border border-emerald-200 text-xs">
                                    {framework.plan}
                                </StatusBadge>
                            </div>
                            <h6 className="font-semibold mb-2">
                                {framework.name}
                            </h6>
                            <div className="mb-3">
                                <div className="flex justify-between text-xs mb-1">
                                    <span>Progress</span>
                                    <span>{framework.progress}%</span>
                                </div>
                                <Progress
                                    percent={framework.progress}
                                    size="sm"
                                />
                            </div>
                            <div className="text-xs text-gray-500">
                                Last updated: {framework.lastUpdated}
                            </div>
                        </div>
                    ))}
                </div>
            </Card>
        </div>
    )
}

// Sample data
const activeFrameworks = [
    {
        id: 1,
        name: 'PCI DSS 4.0',
        category: 'Cybersecurity',
        categoryColor: 'bg-blue-100 text-blue-600 border border-blue-200',
        plan: 'Growth',
        progress: 78,
        lastUpdated: 'Dec 12, 2023',
    },
    {
        id: 2,
        name: 'ISO 27001',
        category: 'Cybersecurity',
        categoryColor: 'bg-blue-100 text-blue-600 border border-blue-200',
        plan: 'Growth',
        progress: 65,
        lastUpdated: 'Dec 5, 2023',
    },
    {
        id: 3,
        name: 'GDPR',
        category: 'Privacy',
        categoryColor: 'bg-purple-100 text-purple-600 border border-purple-200',
        plan: 'Growth',
        progress: 92,
        lastUpdated: 'Dec 15, 2023',
    },
]

export default OverviewTab
