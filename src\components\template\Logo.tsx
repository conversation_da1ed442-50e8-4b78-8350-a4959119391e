import classNames from 'classnames'
import { APP_NAME } from '@/constants/app.constant'
import { PiListChecksFill } from 'react-icons/pi'
import type { CommonProps } from '@/@types/common'

interface LogoProps extends CommonProps {
    type?: 'full' | 'streamline'
    mode?: 'light' | 'dark'
    imgClass?: string
    logoWidth?: number
    logoHeight?: number
}

const Logo = (props: LogoProps) => {
    const {
        type = 'full',
        mode = 'light',
        className,
        imgClass,
        style,
        logoHeight,
    } = props

    const iconSize = logoHeight || (type === 'full' ? 40 : 40)
    const textColor = mode === 'light' ? 'text-gray-900' : 'text-white'
    const iconColor = mode === 'light' ? '#000000' : '#FFFFFF'

    return (
        <div
            className={classNames('logo flex items-center', className)}
            style={style}
        >
            <PiListChecksFill
                size={iconSize}
                color={iconColor}
                className={classNames(imgClass)}
            />

            {type === 'full' && (
                <span
                    className={classNames('ml-2 font-bold text-xl', textColor)}
                >
                    {APP_NAME}
                </span>
            )}
        </div>
    )
}

export default Logo
