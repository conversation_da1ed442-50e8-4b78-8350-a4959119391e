import {
    PiPuzzlePieceDuotone,
    PiChatDotsDuotone,
    PiCalendarDuotone,
    PiUserCircleDuotone,
    PiChatCircleDuotone,
    PiImagesDuotone,
    PiCalendarCheckDuotone,
    PiWarningDuotone,
    PiCursorClickDuotone,
    PiGridFourDuotone,
    PiTextAaDuotone,
    PiSidebarDuotone,
    PiSpinnerGapDuotone,
    PiSkullDuotone,
    PiSpinnerBallDuotone,
    PiBreadDuotone,
    PiMedalDuotone,
    PiCardsDuotone,
    PiTableDuotone,
    PiTagDuotone,
    PiClockCountdownDuotone,
    PiChatCenteredDuotone,
    PiCheckSquareDuotone,
    PiClipboardTextDuotone,
    PiRowsDuotone,
    PiRowsPlusBottomDuotone,
    PiRadioButtonDuotone,
    PiChartDonutDuotone,
    PiListChecksBold,
    PiToggleRightDuotone,
    PiClockAfternoonDuotone,
    PiUploadDuotone,
    PiCaretCircleDownDuotone,
    PiListDuotone,
    PiCodeSimpleDuotone,
    PiFootprintsDuotone,
    PiBrowsersDuotone,
    PiChartLineUpDuotone,
    PiMapTrifoldDuotone,
} from 'react-icons/pi'
import type { IconConfig } from './types'

const componentsIcons: IconConfig = {
    uiComponents: <PiPuzzlePieceDuotone />,
    uiCommonButton: <PiCursorClickDuotone />,
    uiCommonGrid: <PiGridFourDuotone />,
    uiCommonTypography: <PiTextAaDuotone />,
    uiCommonIcons: <PiImagesDuotone />,
    feedback: <PiChatDotsDuotone />,
    uiFeedbackAlert: <PiWarningDuotone />,
    uiFeedbackDialog: <PiChatCircleDuotone />,
    uiFeedbackDrawer: <PiSidebarDuotone />,
    uiFeedbackProgress: <PiSpinnerGapDuotone />,
    uiFeedbackSkeleton: <PiSkullDuotone />,
    uiFeedbackSpinner: <PiSpinnerBallDuotone />,
    uiFeedbackToast: <PiBreadDuotone />,
    uiDataDisplayAvatar: <PiUserCircleDuotone />,
    uiDataDisplayBadge: <PiMedalDuotone />,
    uiDataDisplayCalendar: <PiCalendarDuotone />,
    uiDataDisplayCard: <PiCardsDuotone />,
    uiDataDisplayTable: <PiTableDuotone />,
    uiDataDisplayTag: <PiTagDuotone />,
    uiDataDisplayTimeline: <PiClockCountdownDuotone />,
    uiDataDisplayTooltip: <PiChatCenteredDuotone />,
    uiFormsCheckbox: <PiCheckSquareDuotone />,
    uiFormsDatepicker: <PiCalendarCheckDuotone />,
    uiFormsFormControl: <PiClipboardTextDuotone />,
    uiFormsInput: <PiRowsDuotone />,
    uiFormsInputGroup: <PiRowsPlusBottomDuotone />,
    uiFormsRadio: <PiRadioButtonDuotone />,
    uiFormsSegment: <PiChartDonutDuotone />,
    uiFormsSelect: <PiListChecksBold />,
    uiFormsSwitcher: <PiToggleRightDuotone />,
    uiFormsTimePicker: <PiClockAfternoonDuotone />,
    uiFormsUpload: <PiUploadDuotone />,
    uiNavigationDropdown: <PiCaretCircleDownDuotone />,
    uiNavigationMenu: <PiListDuotone />,
    uiNavigationPagination: <PiCodeSimpleDuotone />,
    uiNavigationSteps: <PiFootprintsDuotone />,
    uiNavigationTabs: <PiBrowsersDuotone />,
    uiGraphChart: <PiChartLineUpDuotone />,
    uiGraphMaps: <PiMapTrifoldDuotone />,
}

export default componentsIcons
