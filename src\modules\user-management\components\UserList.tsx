'use client'

import { useState, useMemo } from 'react'
import { FilterBar } from '@/shared/components'
import { UserFilter } from '../types'
import { FilterOptions } from '@/shared/types/compliance'
import UserCard from './UserCard'
import { User } from '../types'
import Button from '@/components/ui/Button'
import Tabs from '@/components/ui/Tabs'
import {
    HiPlus,
    HiOutlineDownload,
    HiOutlineViewGrid,
    HiOutlineViewList,
} from 'react-icons/hi'

interface UserListProps {
    users: User[]
    loading?: boolean
    onEdit?: (user: User) => void
    onDelete?: (user: User) => void
    onChangeRole?: (user: User, role: 'admin' | 'user') => void
    onChangeStatus?: (
        user: User,
        status: 'active' | 'inactive' | 'suspended',
    ) => void
    onViewPermissions?: (user: User) => void
    onAddUser?: () => void
    onExportCSV?: () => void
    currentUserId?: string
}

const UserList = ({
    users,
    loading = false,
    onEdit,
    onDelete,
    onChangeRole,
    onChangeStatus,
    onViewPermissions,
    onAddUser,
    onExportCSV,
    currentUserId,
}: UserListProps) => {
    const [filters, setFilters] = useState<UserFilter>({})
    const [activeTab, setActiveTab] = useState('all')
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

    // Convert FilterOptions to UserFilter
    const handleFilterChange = (filterOptions: FilterOptions) => {
        const userFilter: UserFilter = {
            search: filterOptions.search,
            role: filterOptions.status?.[0] as 'admin' | 'user' | undefined,
            status: filterOptions.category?.[0] as
                | 'active'
                | 'inactive'
                | 'pending'
                | 'suspended'
                | undefined,
        }
        setFilters(userFilter)
    }

    // Filter options for the FilterBar (mapping role to status, status to category)
    const filterOptions = {
        status: [
            { value: 'admin', label: 'Administrator' },
            { value: 'user', label: 'User' },
        ],
        category: [
            { value: 'active', label: 'Active' },
            { value: 'inactive', label: 'Inactive' },
            { value: 'pending', label: 'Pending' },
            { value: 'suspended', label: 'Suspended' },
        ],
    }

    // Filter users based on active tab and filters
    const filteredUsers = useMemo(() => {
        let filtered = users

        // Filter by tab
        if (activeTab !== 'all') {
            switch (activeTab) {
                case 'admins':
                    filtered = filtered.filter((user) => user.role === 'admin')
                    break
                case 'users':
                    filtered = filtered.filter((user) => user.role === 'user')
                    break
                case 'active':
                    filtered = filtered.filter(
                        (user) => user.status === 'active',
                    )
                    break
                case 'inactive':
                    filtered = filtered.filter(
                        (user) => user.status !== 'active',
                    )
                    break
            }
        }

        // Apply additional filters
        if (filters.role) {
            filtered = filtered.filter((user) => user.role === filters.role)
        }

        if (filters.status) {
            filtered = filtered.filter((user) => user.status === filters.status)
        }

        if (filters.search) {
            const searchLower = filters.search.toLowerCase()
            filtered = filtered.filter(
                (user) =>
                    user.firstName?.toLowerCase().includes(searchLower) ||
                    user.lastName?.toLowerCase().includes(searchLower) ||
                    user.email.toLowerCase().includes(searchLower) ||
                    user.userName.toLowerCase().includes(searchLower),
            )
        }

        return filtered
    }, [users, activeTab, filters])

    // Calculate stats
    const stats = {
        total: users.length,
        admins: users.filter((u) => u.role === 'admin').length,
        users: users.filter((u) => u.role === 'user').length,
        active: users.filter((u) => u.status === 'active').length,
        inactive: users.filter((u) => u.status !== 'active').length,
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 className="text-2xl font-bold">User Management</h1>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">
                        Manage users and their roles in your organization
                    </p>
                </div>
                <div className="flex gap-2">
                    <Button
                        variant="default"
                        icon={<HiOutlineDownload />}
                        onClick={onExportCSV}
                    >
                        Export CSV
                    </Button>
                    <Button
                        variant="solid"
                        icon={<HiPlus />}
                        onClick={onAddUser}
                        className="bg-emerald-500 hover:bg-emerald-600"
                    >
                        Add User
                    </Button>
                </div>
            </div>

            {/* Tabs */}
            <Tabs
                value={activeTab}
                onChange={(val) => setActiveTab(val as string)}
            >
                <Tabs.TabList>
                    <Tabs.TabNav value="all">
                        All Users ({stats.total})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="admins">
                        Administrators ({stats.admins})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="users">
                        Users ({stats.users})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="active">
                        Active ({stats.active})
                    </Tabs.TabNav>
                    <Tabs.TabNav value="inactive">
                        Inactive ({stats.inactive})
                    </Tabs.TabNav>
                </Tabs.TabList>
            </Tabs>

            {/* Filters and View Toggle */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="flex-1 max-w-2xl">
                    <FilterBar
                        searchPlaceholder="Search users..."
                        filters={filterOptions}
                        onFilterChange={handleFilterChange}
                    />
                </div>
                <div className="flex items-center gap-2">
                    <Button
                        size="sm"
                        variant={viewMode === 'grid' ? 'solid' : 'plain'}
                        icon={<HiOutlineViewGrid />}
                        onClick={() => setViewMode('grid')}
                    />
                    <Button
                        size="sm"
                        variant={viewMode === 'list' ? 'solid' : 'plain'}
                        icon={<HiOutlineViewList />}
                        onClick={() => setViewMode('list')}
                    />
                </div>
            </div>

            {/* User Display */}
            {loading ? (
                <div
                    className={
                        viewMode === 'grid'
                            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                            : 'space-y-3'
                    }
                >
                    {Array.from({ length: 6 }).map((_, i) => (
                        <div
                            key={i}
                            className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"
                        ></div>
                    ))}
                </div>
            ) : filteredUsers.length > 0 ? (
                <div
                    className={
                        viewMode === 'grid'
                            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                            : 'space-y-3'
                    }
                >
                    {filteredUsers.map((user) => (
                        <UserCard
                            key={user.id}
                            user={user}
                            compact={viewMode === 'list'}
                            onEdit={onEdit}
                            onDelete={onDelete}
                            onChangeRole={onChangeRole}
                            onChangeStatus={onChangeStatus}
                            onViewPermissions={onViewPermissions}
                            currentUserId={currentUserId}
                        />
                    ))}
                </div>
            ) : (
                <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                        <svg
                            className="w-16 h-16 mx-auto"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={1}
                                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                            />
                        </svg>
                    </div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                        No users found
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                        Get started by adding your first user to the
                        organization.
                    </p>
                    <Button
                        variant="solid"
                        icon={<HiPlus />}
                        onClick={onAddUser}
                        className="bg-emerald-500 hover:bg-emerald-600"
                    >
                        Add User
                    </Button>
                </div>
            )}
        </div>
    )
}

export default UserList
