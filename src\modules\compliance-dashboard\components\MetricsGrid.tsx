import Card from '@/components/ui/Card'
import Progress from '@/components/ui/Progress'
import { DashboardMetrics } from '../types'
import {
    HiOutlineClipboardList,
    HiOutlineCog,
    HiOutlineCheckCircle,
    HiOutlineExclamationCircle,
} from 'react-icons/hi'

interface MetricsGridProps {
    metrics: DashboardMetrics
    loading?: boolean
}

const MetricsGrid = ({ metrics, loading = false }: MetricsGridProps) => {
    if (loading) {
        return (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {Array.from({ length: 8 }).map((_, i) => (
                    <div key={i} className="animate-pulse">
                        <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                    </div>
                ))}
            </div>
        )
    }

    const metricCards = [
        {
            title: 'Frameworks',
            icon: <HiOutlineClipboardList className="w-6 h-6" />,
            value: metrics.frameworks.total,
            subtitle: `${metrics.frameworks.active} active`,
            progress: metrics.frameworks.averageProgress,
            color: 'blue',
            details: [
                { label: 'Active', value: metrics.frameworks.active },
                { label: 'In Progress', value: metrics.frameworks.inProgress },
                { label: 'Not Started', value: metrics.frameworks.notStarted },
            ],
        },
        {
            title: 'Controls',
            icon: <HiOutlineCog className="w-6 h-6" />,
            value: metrics.controls.total,
            subtitle: `${metrics.controls.completionRate}% compliant`,
            progress: metrics.controls.completionRate,
            color: 'emerald',
            details: [
                { label: 'Compliant', value: metrics.controls.compliant },
                { label: 'In Progress', value: metrics.controls.inProgress },
                { label: 'Needs Review', value: metrics.controls.needsReview },
            ],
        },
        {
            title: 'Tasks',
            icon: <HiOutlineCheckCircle className="w-6 h-6" />,
            value: metrics.tasks.total,
            subtitle: `${metrics.tasks.overdue} overdue`,
            progress:
                (metrics.tasks.completed / metrics.tasks.total) * 100 || 0,
            color: metrics.tasks.overdue > 0 ? 'red' : 'green',
            details: [
                { label: 'Open', value: metrics.tasks.open },
                { label: 'In Progress', value: metrics.tasks.inProgress },
                { label: 'Overdue', value: metrics.tasks.overdue },
            ],
        },
        {
            title: 'Risks',
            icon: <HiOutlineExclamationCircle className="w-6 h-6" />,
            value: metrics.risks.total,
            subtitle: `${metrics.risks.critical + metrics.risks.high} high+`,
            progress:
                ((metrics.risks.total - metrics.risks.unmitigated) /
                    metrics.risks.total) *
                    100 || 0,
            color: 'amber',
            details: [
                { label: 'Critical', value: metrics.risks.critical },
                { label: 'High', value: metrics.risks.high },
                { label: 'Unmitigated', value: metrics.risks.unmitigated },
            ],
        },
    ]

    const getColorClasses = (color: string) => {
        const colors = {
            blue: {
                icon: 'text-blue-600 bg-blue-100',
                progress: 'stroke-blue-500',
            },
            emerald: {
                icon: 'text-emerald-600 bg-emerald-100',
                progress: 'stroke-emerald-500',
            },
            green: {
                icon: 'text-green-600 bg-green-100',
                progress: 'stroke-green-500',
            },
            red: {
                icon: 'text-red-600 bg-red-100',
                progress: 'stroke-red-500',
            },
            amber: {
                icon: 'text-amber-600 bg-amber-100',
                progress: 'stroke-amber-500',
            },
        }
        return colors[color as keyof typeof colors] || colors.blue
    }

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {metricCards.map((card, index) => {
                const colorClasses = getColorClasses(card.color)

                return (
                    <Card key={index} className="h-full">
                        <div className="p-6">
                            {/* Header */}
                            <div className="flex items-center justify-between mb-4">
                                <div
                                    className={`p-2 rounded-lg ${colorClasses.icon}`}
                                >
                                    {card.icon}
                                </div>
                                <div className="text-right">
                                    <div className="text-2xl font-bold">
                                        {card.value}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                        {card.subtitle}
                                    </div>
                                </div>
                            </div>

                            {/* Title */}
                            <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
                                {card.title}
                            </h3>

                            {/* Progress */}
                            <div className="mb-4">
                                <div className="flex justify-between text-sm mb-1">
                                    <span className="text-gray-600 dark:text-gray-400">
                                        Progress
                                    </span>
                                    <span className="font-medium">
                                        {Math.round(card.progress)}%
                                    </span>
                                </div>
                                <Progress percent={card.progress} size="sm" />
                            </div>

                            {/* Details */}
                            <div className="space-y-2">
                                {card.details.map((detail, detailIndex) => (
                                    <div
                                        key={detailIndex}
                                        className="flex justify-between text-sm"
                                    >
                                        <span className="text-gray-600 dark:text-gray-400">
                                            {detail.label}
                                        </span>
                                        <span className="font-medium">
                                            {detail.value}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </Card>
                )
            })}
        </div>
    )
}

export default MetricsGrid
