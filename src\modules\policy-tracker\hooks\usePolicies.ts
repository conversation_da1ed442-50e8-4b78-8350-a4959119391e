'use client'

import { useState, useCallback } from 'react'
import {
    usePolicies as usePoliciesBase,
    useSupabaseMutation,
} from '@/shared/hooks'
import {
    Policy,
    FilterOptions,
    PaginationOptions,
} from '@/shared/types/compliance'
import { exportToCSV } from '@/shared/utils/compliance'

export const usePolicies = (
    filters?: FilterOptions,
    pagination?: PaginationOptions,
) => {
    return usePoliciesBase(filters, pagination)
}

export const usePolicyMutations = () => {
    const { create, update, remove, loading, error } =
        useSupabaseMutation<Policy>('policies')

    const createPolicy = useCallback(
        async (policyData: Partial<Policy>) => {
            const newPolicy = {
                ...policyData,
                id: crypto.randomUUID(),
                version: policyData.version || '1.0',
                status: 'Draft' as const,
                controlIds: policyData.controlIds || [],
                acknowledgments: [],
                tags: policyData.tags || [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                organizationId: 'default-org', // This should come from auth context
            }

            return await create(newPolicy)
        },
        [create],
    )

    const updatePolicy = useCallback(
        async (id: string, updates: Partial<Policy>) => {
            const updatedData = {
                ...updates,
                updatedAt: new Date().toISOString(),
            }

            return await update(id, updatedData)
        },
        [update],
    )

    const deletePolicy = useCallback(
        async (id: string) => {
            return await remove(id)
        },
        [remove],
    )

    const publishPolicy = useCallback(
        async (id: string) => {
            return await updatePolicy(id, {
                status: 'Published',
                effectiveDate: new Date().toISOString().split('T')[0],
            })
        },
        [updatePolicy],
    )

    return {
        createPolicy,
        updatePolicy,
        deletePolicy,
        publishPolicy,
        loading,
        error,
    }
}

export const usePolicyExport = () => {
    const exportPoliciesToCSV = useCallback((policies: Policy[]) => {
        const exportData = policies.map((policy) => ({
            'Policy ID': policy.id,
            Title: policy.title,
            Version: policy.version,
            Status: policy.status,
            Category: policy.category || '',
            Description: policy.description || '',
            Owner: policy.owner || '',
            Approver: policy.approver || '',
            'Effective Date': policy.effectiveDate || '',
            'Review Date': policy.reviewDate || '',
            'Next Review Date': policy.nextReviewDate || '',
            Controls: policy.controlIds.length,
            Acknowledgments: policy.acknowledgments.length,
            Tags: policy.tags.join(', '),
            'Created Date': policy.createdAt,
            'Updated Date': policy.updatedAt,
        }))

        exportToCSV(
            exportData,
            `policies-export-${new Date().toISOString().split('T')[0]}`,
        )
    }, [])

    return { exportPoliciesToCSV }
}

// Mock data for development - remove when Supabase is set up
export const useMockPolicies = () => {
    const [policies] = useState<Policy[]>([
        {
            id: 'policy-1',
            title: 'Information Security Policy',
            description:
                'Comprehensive policy covering information security requirements, access controls, and data protection measures in accordance with PCI DSS 4.0.1.',
            content:
                '# Information Security Policy\n\n## Purpose\nThis policy establishes the framework for protecting information assets...',
            version: '2.1',
            status: 'Published',
            category: 'Security',
            owner: 'security-team',
            approver: 'ciso',
            effectiveDate: '2023-01-01',
            reviewDate: '2023-06-01',
            nextReviewDate: '2024-06-01',
            controlIds: ['pci-12.1.1', 'pci-12.1.2'],
            acknowledgments: [
                {
                    id: 'ack-1',
                    policyId: 'policy-1',
                    userId: 'user-1',
                    acknowledgedAt: '2023-01-15T00:00:00Z',
                    version: '2.1',
                },
                {
                    id: 'ack-2',
                    policyId: 'policy-1',
                    userId: 'user-2',
                    acknowledgedAt: '2023-01-20T00:00:00Z',
                    version: '2.1',
                },
            ],
            tags: ['security', 'pci-dss', 'mandatory'],
            createdAt: '2022-12-01T00:00:00Z',
            updatedAt: '2023-01-01T00:00:00Z',
            organizationId: 'default-org',
        },
        {
            id: 'policy-2',
            title: 'Access Control Policy',
            description:
                'Policy defining user access management, role-based access controls, and privileged access procedures.',
            content:
                '# Access Control Policy\n\n## Scope\nThis policy applies to all systems that store, process, or transmit cardholder data...',
            version: '1.3',
            status: 'Published',
            category: 'Security',
            owner: 'it-team',
            approver: 'ciso',
            effectiveDate: '2023-03-01',
            reviewDate: '2023-09-01',
            nextReviewDate: '2024-03-01',
            controlIds: ['pci-7.1.1', 'pci-8.1.1'],
            acknowledgments: [
                {
                    id: 'ack-3',
                    policyId: 'policy-2',
                    userId: 'user-1',
                    acknowledgedAt: '2023-03-05T00:00:00Z',
                    version: '1.3',
                },
            ],
            tags: ['access-control', 'rbac', 'pci-dss'],
            createdAt: '2023-02-01T00:00:00Z',
            updatedAt: '2023-03-01T00:00:00Z',
            organizationId: 'default-org',
        },
        {
            id: 'policy-3',
            title: 'Incident Response Policy',
            description:
                'Procedures for detecting, responding to, and recovering from security incidents affecting cardholder data.',
            content:
                '# Incident Response Policy\n\n## Incident Classification\n1. Critical: Confirmed or suspected cardholder data breach...',
            version: '1.0',
            status: 'Under Review',
            category: 'Security',
            owner: 'security-team',
            approver: 'ciso',
            reviewDate: '2024-01-10',
            nextReviewDate: '2024-01-31',
            controlIds: ['pci-12.10.1'],
            acknowledgments: [],
            tags: ['incident-response', 'security', 'draft'],
            createdAt: '2024-01-05T00:00:00Z',
            updatedAt: '2024-01-10T00:00:00Z',
            organizationId: 'default-org',
        },
        {
            id: 'policy-4',
            title: 'Data Retention Policy',
            description:
                'Guidelines for data retention periods, secure disposal, and cardholder data lifecycle management.',
            content:
                '# Data Retention Policy\n\n## Cardholder Data Retention\nCardholder data must not be stored longer than necessary...',
            version: '2.0',
            status: 'Published',
            category: 'Privacy',
            owner: 'compliance-team',
            approver: 'dpo',
            effectiveDate: '2023-07-01',
            reviewDate: '2023-12-01',
            nextReviewDate: '2024-01-15',
            controlIds: ['pci-3.1.1', 'pci-3.1.2'],
            acknowledgments: [
                {
                    id: 'ack-4',
                    policyId: 'policy-4',
                    userId: 'user-2',
                    acknowledgedAt: '2023-07-05T00:00:00Z',
                    version: '2.0',
                },
            ],
            tags: ['data-retention', 'privacy', 'cardholder-data'],
            createdAt: '2023-06-01T00:00:00Z',
            updatedAt: '2023-07-01T00:00:00Z',
            organizationId: 'default-org',
        },
        {
            id: 'policy-5',
            title: 'Remote Work Security Policy',
            description:
                'Security requirements for remote work environments and access to cardholder data from remote locations.',
            content:
                '# Remote Work Security Policy\n\n## VPN Requirements\nAll remote access must use approved VPN solutions...',
            version: '1.1',
            status: 'Draft',
            category: 'IT',
            owner: 'it-team',
            nextReviewDate: '2024-02-01',
            controlIds: ['pci-4.1.1'],
            acknowledgments: [],
            tags: ['remote-work', 'vpn', 'security'],
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-08T00:00:00Z',
            organizationId: 'default-org',
        },
    ])

    return {
        data: policies,
        loading: false,
        error: null,
        total: policies.length,
        refetch: () => {},
    }
}
